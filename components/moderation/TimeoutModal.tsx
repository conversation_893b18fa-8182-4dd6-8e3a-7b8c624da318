"use client";

import React, { useState } from 'react';

interface TimeoutModalProps {
  targetUserId: string;
  onClose: () => void;
  onTimeout: (userId: string, duration: number, reason?: string) => Promise<void>;
}

export function TimeoutModal({ targetUserId, onClose, onTimeout }: TimeoutModalProps) {
  const [duration, setDuration] = useState(5);
  const [reason, setReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const presetDurations = [
    { label: '1 minute', value: 1 },
    { label: '5 minutes', value: 5 },
    { label: '10 minutes', value: 10 },
    { label: '30 minutes', value: 30 },
    { label: '1 hour', value: 60 },
  ];

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await onTimeout(targetUserId, duration, reason || undefined);
      onClose();
    } catch (error) {
      console.error('Failed to timeout participant:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">Timeout Participant</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Duration</label>
            <div className="grid grid-cols-2 gap-2 mb-2">
              {presetDurations.map((preset) => (
                <button
                  key={preset.value}
                  onClick={() => setDuration(preset.value)}
                  className={`p-2 text-sm border rounded ${
                    duration === preset.value
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {preset.label}
                </button>
              ))}
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="number"
                value={duration}
                onChange={(e) => setDuration(parseInt(e.target.value) || 1)}
                min="1"
                max="1440"
                className="flex-1 p-2 border rounded focus:ring-2 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-500">minutes</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Reason (optional)</label>
            <textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500"
              rows={3}
              placeholder="Enter reason for timeout..."
            />
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
            <p className="text-sm text-yellow-800">
              The participant will be disconnected and unable to rejoin for {duration} minute{duration !== 1 ? 's' : ''}.
            </p>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
          >
            {isSubmitting ? 'Processing...' : 'Apply Timeout'}
          </button>
        </div>
      </div>
    </div>
  );
}