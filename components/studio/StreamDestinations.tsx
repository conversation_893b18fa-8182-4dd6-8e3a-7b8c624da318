"use client";

import React, { useState } from 'react';
import { 
  Plus, 
  Settings, 
  Trash2, 
  ExternalLink, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Youtube,
  Facebook,
  Twitch,
  Instagram,
  Twitter,
  Linkedin,
  Globe,
  Key,
  Eye,
  EyeOff,
  Copy,
  RefreshCw
} from 'lucide-react';

interface StreamDestination {
  id: string;
  platform: 'youtube' | 'facebook' | 'twitch' | 'instagram' | 'twitter' | 'linkedin' | 'custom';
  name: string;
  status: 'connected' | 'disconnected' | 'error' | 'streaming';
  streamKey?: string;
  rtmpUrl?: string;
  isEnabled: boolean;
  lastUsed?: Date;
  viewerCount?: number;
}

interface StreamDestinationsProps {
  isOpen: boolean;
  onClose: () => void;
  destinations: StreamDestination[];
  onUpdateDestinations: (destinations: StreamDestination[]) => void;
}

const platformIcons = {
  youtube: Youtube,
  facebook: Facebook,
  twitch: Twitch,
  instagram: Instagram,
  twitter: Twitter,
  linkedin: Linkedin,
  custom: Globe,
};

const platformColors = {
  youtube: 'text-red-600 bg-red-100',
  facebook: 'text-blue-600 bg-blue-100',
  twitch: 'text-purple-600 bg-purple-100',
  instagram: 'text-pink-600 bg-pink-100',
  twitter: 'text-blue-400 bg-blue-50',
  linkedin: 'text-blue-700 bg-blue-100',
  custom: 'text-gray-600 bg-gray-100',
};

export function StreamDestinations({ isOpen, onClose, destinations, onUpdateDestinations }: StreamDestinationsProps) {
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<StreamDestination['platform']>('youtube');
  const [streamKey, setStreamKey] = useState('');
  const [rtmpUrl, setRtmpUrl] = useState('');
  const [customName, setCustomName] = useState('');
  const [showStreamKey, setShowStreamKey] = useState(false);

  const addDestination = () => {
    const newDestination: StreamDestination = {
      id: `dest-${Date.now()}`,
      platform: selectedPlatform,
      name: selectedPlatform === 'custom' ? customName : selectedPlatform.charAt(0).toUpperCase() + selectedPlatform.slice(1),
      status: 'disconnected',
      streamKey,
      rtmpUrl: selectedPlatform === 'custom' ? rtmpUrl : getDefaultRtmpUrl(selectedPlatform),
      isEnabled: true,
    };

    onUpdateDestinations([...destinations, newDestination]);
    setShowAddModal(false);
    setStreamKey('');
    setRtmpUrl('');
    setCustomName('');
  };

  const getDefaultRtmpUrl = (platform: string) => {
    const urls = {
      youtube: 'rtmp://a.rtmp.youtube.com/live2',
      facebook: 'rtmps://live-api-s.facebook.com:443/rtmp',
      twitch: 'rtmp://live.twitch.tv/live',
      instagram: 'rtmps://live-upload.instagram.com:443/rtmp',
      twitter: 'rtmp://live.twitter.com/live',
      linkedin: 'rtmp://live.linkedin.com/live',
    };
    return urls[platform as keyof typeof urls] || '';
  };

  const toggleDestination = (id: string) => {
    const updatedDestinations = destinations.map(dest =>
      dest.id === id ? { ...dest, isEnabled: !dest.isEnabled } : dest
    );
    onUpdateDestinations(updatedDestinations);
  };

  const removeDestination = (id: string) => {
    onUpdateDestinations(destinations.filter(dest => dest.id !== id));
  };

  const testConnection = (id: string) => {
    // Simulate connection test
    const updatedDestinations = destinations.map(dest =>
      dest.id === id ? { ...dest, status: 'connected' as const } : dest
    );
    onUpdateDestinations(updatedDestinations);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-900 rounded-lg w-full max-w-4xl h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Stream Destinations</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white transition-colors"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-medium text-white">Connected Platforms</h3>
              <p className="text-sm text-gray-400">Manage where your stream will be broadcast</p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add Destination</span>
            </button>
          </div>

          {/* Destinations Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {destinations.map((destination) => {
              const Icon = platformIcons[destination.platform];
              const colorClass = platformColors[destination.platform];
              
              return (
                <div key={destination.id} className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${colorClass}`}>
                        <Icon className="w-5 h-5" />
                      </div>
                      <div>
                        <h4 className="font-medium text-white">{destination.name}</h4>
                        <div className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${
                            destination.status === 'connected' ? 'bg-green-500' :
                            destination.status === 'streaming' ? 'bg-red-500 animate-pulse' :
                            destination.status === 'error' ? 'bg-red-500' :
                            'bg-gray-500'
                          }`}></div>
                          <span className="text-xs text-gray-400 capitalize">{destination.status}</span>
                          {destination.status === 'streaming' && destination.viewerCount !== undefined && (
                            <span className="text-xs text-gray-400">• {destination.viewerCount} viewers</span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => toggleDestination(destination.id)}
                        className={`p-2 rounded transition-colors ${
                          destination.isEnabled 
                            ? 'bg-green-600 text-white' 
                            : 'bg-gray-600 text-gray-300'
                        }`}
                      >
                        {destination.isEnabled ? <CheckCircle className="w-4 h-4" /> : <XCircle className="w-4 h-4" />}
                      </button>
                      <button
                        onClick={() => testConnection(destination.id)}
                        className="p-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                      >
                        <RefreshCw className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => removeDestination(destination.id)}
                        className="p-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  {destination.streamKey && (
                    <div className="mt-3 p-3 bg-gray-700 rounded">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs text-gray-400">Stream Key</span>
                        <button
                          onClick={() => setShowStreamKey(!showStreamKey)}
                          className="text-gray-400 hover:text-white"
                        >
                          {showStreamKey ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
                        </button>
                      </div>
                      <div className="flex items-center space-x-2">
                        <code className="text-xs text-gray-300 font-mono flex-1 truncate">
                          {showStreamKey ? destination.streamKey : '••••••••••••••••'}
                        </code>
                        <button
                          onClick={() => navigator.clipboard.writeText(destination.streamKey || '')}
                          className="p-1 text-gray-400 hover:text-white"
                        >
                          <Copy className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {destinations.length === 0 && (
            <div className="text-center py-12">
              <Globe className="w-16 h-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No destinations configured</h3>
              <p className="text-gray-400 mb-6">Add streaming destinations to broadcast your content</p>
              <button
                onClick={() => setShowAddModal(true)}
                className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-5 h-5" />
                <span>Add Your First Destination</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Add Destination Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-60">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Add Stream Destination</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Platform</label>
                <select
                  value={selectedPlatform}
                  onChange={(e) => setSelectedPlatform(e.target.value as StreamDestination['platform'])}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                >
                  <option value="youtube">YouTube</option>
                  <option value="facebook">Facebook</option>
                  <option value="twitch">Twitch</option>
                  <option value="instagram">Instagram</option>
                  <option value="twitter">Twitter</option>
                  <option value="linkedin">LinkedIn</option>
                  <option value="custom">Custom RTMP</option>
                </select>
              </div>

              {selectedPlatform === 'custom' && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Name</label>
                  <input
                    type="text"
                    value={customName}
                    onChange={(e) => setCustomName(e.target.value)}
                    placeholder="Custom destination name"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Stream Key</label>
                <input
                  type="password"
                  value={streamKey}
                  onChange={(e) => setStreamKey(e.target.value)}
                  placeholder="Enter your stream key"
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                />
              </div>

              {selectedPlatform === 'custom' && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">RTMP URL</label>
                  <input
                    type="text"
                    value={rtmpUrl}
                    onChange={(e) => setRtmpUrl(e.target.value)}
                    placeholder="rtmp://your-server.com/live"
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white"
                  />
                </div>
              )}

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={addDestination}
                  disabled={!streamKey || (selectedPlatform === 'custom' && (!customName || !rtmpUrl))}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Add Destination
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
