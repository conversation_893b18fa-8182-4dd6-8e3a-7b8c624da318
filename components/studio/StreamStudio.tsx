"use client";

import React, { useState } from 'react';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { ModerationPanel } from '../moderation/ModerationPanel';
import { BreakoutRoomManager } from '../moderation/BreakoutRoomManager';
import { PermissionControl } from '../moderation/PermissionControl';
import { VideoPlayer } from '../video-player';

interface StreamStudioProps {
  streamId: Id<"streams">;
  currentUserId: string;
}

export function StreamStudio({ streamId, currentUserId }: StreamStudioProps) {
  const stream = useQuery(api.streams.get, { streamId });
  const currentUser = useQuery(api.users.getCurrentUser);
  const startStream = useMutation(api.streams.startStream);
  const endStream = useMutation(api.streams.endStream);
  const createToken = useAction(api.livekit.createToken);
  
  const [activeTab, setActiveTab] = useState<'studio' | 'moderation' | 'breakout' | 'permissions'>('studio');
  const [token, setToken] = useState<string | null>(null);

  // Generate LiveKit token when component mounts
  React.useEffect(() => {
    const generateToken = async () => {
      try {
        const liveKitToken = await createToken({
          streamId,
          viewerName: currentUser?.username || 'Anonymous',
        });
        setToken(liveKitToken);
      } catch (error) {
        console.error('Failed to generate token:', error);
      }
    };

    if (currentUser) {
      generateToken();
    }
  }, [createToken, streamId, currentUser]);

  const handleStartStream = async () => {
    try {
      await startStream({ streamId });
    } catch (error) {
      console.error('Failed to start stream:', error);
    }
  };

  const handleEndStream = async () => {
    try {
      await endStream({ streamId });
    } catch (error) {
      console.error('Failed to end stream:', error);
    }
  };

  if (!stream || !currentUser) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }

  const canModerate = currentUser.globalRole === 'master' || 
                     currentUser.globalRole === 'admin' || 
                     stream.hostId === currentUserId;

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold">{stream.title}</h1>
            <p className="text-sm text-gray-500">
              Status: <span className={`font-medium ${
                stream.isLive ? 'text-green-600' : 'text-gray-600'
              }`}>
                {stream.isLive ? 'Live' : 'Offline'}
              </span>
              {stream.participantCount !== undefined && (
                <span className="ml-4">
                  Participants: {stream.participantCount}
                </span>
              )}
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Stream Controls */}
            {canModerate && (
              <div className="flex space-x-2">
                {!stream.isLive ? (
                  <button
                    onClick={handleStartStream}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                  >
                    Start Stream
                  </button>
                ) : (
                  <button
                    onClick={handleEndStream}
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                  >
                    End Stream
                  </button>
                )}
              </div>
            )}
            
            {/* Tab Navigation */}
            {canModerate && (
              <div className="flex space-x-2">
                <button
                  onClick={() => setActiveTab('studio')}
                  className={`px-4 py-2 rounded ${
                    activeTab === 'studio' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Studio
                </button>
                <button
                  onClick={() => setActiveTab('moderation')}
                  className={`px-4 py-2 rounded ${
                    activeTab === 'moderation' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Moderation
                </button>
                <button
                  onClick={() => setActiveTab('breakout')}
                  className={`px-4 py-2 rounded ${
                    activeTab === 'breakout' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Breakout Rooms
                </button>
                <button
                  onClick={() => setActiveTab('permissions')}
                  className={`px-4 py-2 rounded ${
                    activeTab === 'permissions' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  Permissions
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Left Panel - Main Content */}
        <div className="flex-1 p-6">
          {activeTab === 'studio' && (
            <div className="bg-white rounded-lg shadow h-full flex items-center justify-center">
              {token ? (
                <VideoPlayer token={token} room={streamId} />
              ) : (
                <div>Loading video...</div>
              )}
            </div>
          )}
          {activeTab === 'moderation' && canModerate && (
            <ModerationPanel 
              streamId={streamId} 
              currentUserId={currentUserId}
            />
          )}
          {activeTab === 'breakout' && canModerate && (
            <BreakoutRoomManager 
              streamId={streamId} 
              currentUserId={currentUserId}
            />
          )}
          {activeTab === 'permissions' && canModerate && (
            <PermissionControl 
              streamId={streamId} 
              currentUserId={currentUserId}
            />
          )}
        </div>

        {/* Right Panel - Chat and Controls */}
        <div className="w-80 bg-white border-l p-4 space-y-4">
          {/* Stream Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium mb-2">Stream Info</h3>
            <div className="text-sm space-y-1">
              <div>Host: {stream.host?.username || 'Unknown'}</div>
              <div>Max Participants: {stream.maxParticipants || 50}</div>
              <div>Chat: {stream.isChatEnabled ? 'Enabled' : 'Disabled'}</div>
            </div>
          </div>

          {/* Chat Placeholder */}
          <div className="bg-gray-50 rounded-lg p-4 flex-1">
            <h3 className="font-medium mb-2">Chat</h3>
            <div className="text-sm text-gray-500">
              Chat functionality would be implemented here
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}