"use client";

import React, { useState } from 'react';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { ModerationPanel } from '../moderation/ModerationPanel';
import { BreakoutRoomManager } from '../moderation/BreakoutRoomManager';
import { PermissionControl } from '../moderation/PermissionControl';
import { VideoPlayer } from '../video-player';
import { LayoutManager } from './LayoutManager';
import { CustomLayoutBuilder } from './CustomLayoutBuilder';
import { BrandingPanel } from './BrandingPanel';
import { GuestManager } from './GuestManager';
import { StreamDestinations } from './StreamDestinations';
import { StreamMonitor } from './StreamMonitor';
import { StreamChat } from './StreamChat';
import {
  Play,
  Square,
  Users,
  Settings,
  Monitor,
  Mic,
  MicOff,
  Video,
  VideoOff,
  ScreenShare,
  MessageCircle,
  MoreHorizontal,
  Eye,
  EyeOff,
  Volume2,
  VolumeX,
  Circle,
  Layers,
  Image,
  Type,
  Palette,
  UserPlus,
  Grid3X3,
  Maximize2,
  Minimize2
} from 'lucide-react';

interface StreamStudioProps {
  streamId: Id<"streams">;
  currentUserId: string;
}

export function StreamStudio({ streamId, currentUserId }: StreamStudioProps) {
  const stream = useQuery(api.streams.get, { streamId });
  const currentUser = useQuery(api.users.getCurrentUser);
  const startStream = useMutation(api.streams.startStream);
  const endStream = useMutation(api.streams.endStream);
  const createToken = useAction(api.livekit.createToken);

  const [activePanel, setActivePanel] = useState<'none' | 'chat' | 'participants' | 'settings'>('none');
  const [selectedLayout, setSelectedLayout] = useState<string>('single');
  const [isRecording, setIsRecording] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [showLayoutBuilder, setShowLayoutBuilder] = useState(false);
  const [showBrandingPanel, setShowBrandingPanel] = useState(false);
  const [showGuestManager, setShowGuestManager] = useState(false);
  const [showDestinations, setShowDestinations] = useState(false);
  const [showMonitor, setShowMonitor] = useState(false);
  const [customLayouts, setCustomLayouts] = useState<any[]>([]);
  const [brandElements, setBrandElements] = useState<any[]>([]);
  const [guests, setGuests] = useState<any[]>([]);
  const [destinations, setDestinations] = useState<any[]>([]);
  const [isChatVisible, setIsChatVisible] = useState(true);
  const [token, setToken] = useState<string | null>(null);

  // StreamYard layout presets
  const layouts = [
    { id: 'single', name: 'Single', icon: '👤' },
    { id: 'group', name: 'Group', icon: '👥' },
    { id: 'spotlight', name: 'Spotlight', icon: '⭐' },
    { id: 'news', name: 'News', icon: '📺' },
    { id: 'screen', name: 'Screen', icon: '🖥️' },
    { id: 'pip', name: 'Picture-in-Picture', icon: '📱' },
    { id: 'cinema', name: 'Cinema', icon: '🎬' },
  ];

  // Generate LiveKit token when component mounts
  React.useEffect(() => {
    const generateToken = async () => {
      try {
        const liveKitToken = await createToken({
          streamId,
          viewerName: currentUser?.username || 'Anonymous',
        });
        setToken(liveKitToken);
      } catch (error) {
        console.error('Failed to generate token:', error);
      }
    };

    if (currentUser) {
      generateToken();
    }
  }, [createToken, streamId, currentUser]);

  const handleStartStream = async () => {
    try {
      await startStream({ streamId });
    } catch (error) {
      console.error('Failed to start stream:', error);
    }
  };

  const handleEndStream = async () => {
    try {
      await endStream({ streamId });
    } catch (error) {
      console.error('Failed to end stream:', error);
    }
  };

  const toggleMute = () => setIsMuted(!isMuted);
  const toggleVideo = () => setIsVideoOff(!isVideoOff);
  const toggleScreenShare = () => setIsScreenSharing(!isScreenSharing);
  const toggleRecording = () => setIsRecording(!isRecording);

  const handleLayoutChange = (layoutId: string) => {
    setSelectedLayout(layoutId);
    // Here you would implement the actual layout change logic
    console.log('Layout changed to:', layoutId);
  };

  const handleCreateCustomLayout = () => {
    setShowLayoutBuilder(true);
  };

  const handleSaveCustomLayout = (layout: any) => {
    setCustomLayouts([...customLayouts, layout]);
    setSelectedLayout(layout.id);
  };

  if (!stream || !currentUser) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900">
        <div className="text-white text-lg">Loading studio...</div>
      </div>
    );
  }

  const canModerate = currentUser.globalRole === 'master' ||
                     currentUser.globalRole === 'admin' ||
                     stream.hostId === currentUserId;

  return (
    <div className="h-screen flex flex-col bg-gray-900">
      {/* StreamYard-style Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-4 md:px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 md:space-x-4 min-w-0 flex-1">
            <h1 className="text-lg md:text-xl font-semibold text-white truncate">{stream.title}</h1>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${stream.isLive ? 'bg-red-500 animate-pulse' : 'bg-gray-500'}`}></div>
              <span className="text-sm text-gray-300">
                {stream.isLive ? 'LIVE' : 'OFFLINE'}
              </span>
              {stream.participantCount !== undefined && (
                <span className="text-sm text-gray-400 hidden sm:flex items-center ml-4">
                  <Users className="w-4 h-4 mr-1" />
                  {stream.participantCount}
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2 md:space-x-3">
            {/* Stream Status and Controls */}
            <div className="flex items-center space-x-2 md:space-x-4">
              <button
                onClick={() => setShowMonitor(true)}
                className="flex items-center space-x-1 md:space-x-2 text-sm text-gray-300 hover:text-white transition-colors"
              >
                <Eye className="w-4 h-4" />
                <span className="hidden sm:inline">0 viewers</span>
              </button>

              <button
                onClick={() => setShowDestinations(true)}
                className="flex items-center space-x-1 md:space-x-2 text-sm text-gray-300 hover:text-white transition-colors"
              >
                <Monitor className="w-4 h-4" />
                <span className="hidden md:inline">Destinations</span>
              </button>
            </div>

            {canModerate && (
              <div className="flex space-x-2">
                {!stream.isLive ? (
                  <button
                    onClick={handleStartStream}
                    className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    <Play className="w-4 h-4" />
                    <span>Go Live</span>
                  </button>
                ) : (
                  <button
                    onClick={handleEndStream}
                    className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    <Square className="w-4 h-4" />
                    <span>End Stream</span>
                  </button>
                )}
              </div>
            )}

            {/* Settings and More */}
            <button
              onClick={() => setActivePanel(activePanel === 'settings' ? 'none' : 'settings')}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Studio Area */}
      <div className="flex-1 flex relative">
        {/* Main Stage Area */}
        <div className="flex-1 flex flex-col">
          {/* Stage */}
          <div className="flex-1 p-4">
            <div className="h-full bg-black rounded-lg relative overflow-hidden border border-gray-700">
              {token ? (
                <VideoPlayer token={token} room={streamId} />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <div className="text-center">
                    <Monitor className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p>Loading studio...</p>
                  </div>
                </div>
              )}

              {/* Stage Overlay Controls */}
              <div className="absolute top-4 left-4 flex space-x-2">
                <button
                  onClick={() => setShowBrandingPanel(true)}
                  className="p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors"
                  title="Branding & Graphics"
                >
                  <Layers className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setShowBrandingPanel(true)}
                  className="p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors"
                  title="Add Logo"
                >
                  <Image className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setShowBrandingPanel(true)}
                  className="p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors"
                  title="Add Text"
                >
                  <Type className="w-4 h-4" />
                </button>
              </div>

              {/* Recording Indicator */}
              {isRecording && (
                <div className="absolute top-4 right-4 flex items-center space-x-2 bg-red-600 text-white px-3 py-1 rounded-lg">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium">REC</span>
                </div>
              )}
            </div>
          </div>

          {/* Layout Controls */}
          <div className="bg-gray-800 border-t border-gray-700 p-2 md:p-4">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-3 md:space-y-0">
              <div className="flex-1 overflow-x-auto">
                <LayoutManager
                  selectedLayout={selectedLayout}
                  onLayoutChange={handleLayoutChange}
                  onCreateCustomLayout={handleCreateCustomLayout}
                  customLayouts={customLayouts}
                />
              </div>

              <div className="flex items-center justify-center md:justify-start space-x-2 md:ml-4">
                <button
                  onClick={() => setShowGuestManager(true)}
                  className="flex items-center space-x-2 px-3 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors text-sm"
                >
                  <UserPlus className="w-4 h-4" />
                  <span className="hidden sm:inline">Invite Guests</span>
                  <span className="sm:hidden">Invite</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side Panel */}
        {activePanel !== 'none' && (
          <div className="w-full md:w-80 bg-gray-800 border-l border-gray-700 flex flex-col absolute md:relative inset-0 md:inset-auto z-10 md:z-auto">
            {/* Panel Header */}
            <div className="p-4 border-b border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-white">
                  {activePanel === 'chat' && 'Chat'}
                  {activePanel === 'participants' && 'Participants'}
                  {activePanel === 'settings' && 'Settings'}
                </h3>
                <button
                  onClick={() => setActivePanel('none')}
                  className="p-1 text-gray-400 hover:text-white transition-colors"
                >
                  <Minimize2 className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Panel Content */}
            <div className="flex-1 p-4 overflow-y-auto">
              {activePanel === 'chat' && (
                <div className="h-full -m-4">
                  <StreamChat
                    streamId={streamId}
                    currentUserId={currentUserId}
                    isModerator={canModerate}
                    isVisible={isChatVisible}
                    onToggleVisibility={() => setIsChatVisible(!isChatVisible)}
                  />
                </div>
              )}

              {activePanel === 'participants' && (
                <div className="space-y-4">
                  <button className="w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <UserPlus className="w-4 h-4" />
                    <span>Invite Guests</span>
                  </button>
                  <div className="text-sm text-gray-400">No participants yet</div>
                </div>
              )}

              {activePanel === 'settings' && canModerate && (
                <div className="space-y-4">
                  <ModerationPanel
                    streamId={streamId}
                    currentUserId={currentUserId}
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Bottom Control Panel */}
      <div className="bg-gray-800 border-t border-gray-700 p-2 md:p-4">
        <div className="flex items-center justify-between">
          {/* Left Controls */}
          <div className="flex items-center space-x-2 md:space-x-3">
            <button
              onClick={toggleMute}
              className={`p-2 md:p-3 rounded-lg transition-colors ${
                isMuted
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {isMuted ? <MicOff className="w-4 h-4 md:w-5 md:h-5" /> : <Mic className="w-4 h-4 md:w-5 md:h-5" />}
            </button>

            <button
              onClick={toggleVideo}
              className={`p-2 md:p-3 rounded-lg transition-colors ${
                isVideoOff
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {isVideoOff ? <VideoOff className="w-4 h-4 md:w-5 md:h-5" /> : <Video className="w-4 h-4 md:w-5 md:h-5" />}
            </button>

            <button
              onClick={toggleScreenShare}
              className={`p-2 md:p-3 rounded-lg transition-colors ${
                isScreenSharing
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <ScreenShare className="w-4 h-4 md:w-5 md:h-5" />
            </button>
          </div>

          {/* Center Controls */}
          <div className="flex items-center space-x-2 md:space-x-3">
            <button
              onClick={toggleRecording}
              className={`flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 rounded-lg transition-colors text-sm ${
                isRecording
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              <Circle className="w-4 h-4" />
              <span className="hidden sm:inline">{isRecording ? 'Stop Recording' : 'Record'}</span>
              <span className="sm:hidden">{isRecording ? 'Stop' : 'Rec'}</span>
            </button>
          </div>

          {/* Right Controls */}
          <div className="flex items-center space-x-2 md:space-x-3">
            <button
              onClick={() => setActivePanel(activePanel === 'chat' ? 'none' : 'chat')}
              className="p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
            >
              <MessageCircle className="w-4 h-4 md:w-5 md:h-5" />
            </button>

            <button
              onClick={() => setActivePanel(activePanel === 'participants' ? 'none' : 'participants')}
              className="p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors"
            >
              <Users className="w-4 h-4 md:w-5 md:h-5" />
            </button>

            <button className="p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors">
              <MoreHorizontal className="w-4 h-4 md:w-5 md:h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Custom Layout Builder Modal */}
      <CustomLayoutBuilder
        isOpen={showLayoutBuilder}
        onClose={() => setShowLayoutBuilder(false)}
        onSave={handleSaveCustomLayout}
      />

      {/* Branding Panel Modal */}
      <BrandingPanel
        isOpen={showBrandingPanel}
        onClose={() => setShowBrandingPanel(false)}
        elements={brandElements}
        onUpdateElements={setBrandElements}
      />

      {/* Guest Manager Modal */}
      <GuestManager
        isOpen={showGuestManager}
        onClose={() => setShowGuestManager(false)}
        guests={guests}
        onUpdateGuests={setGuests}
        streamId={streamId}
      />

      {/* Stream Destinations Modal */}
      <StreamDestinations
        isOpen={showDestinations}
        onClose={() => setShowDestinations(false)}
        destinations={destinations}
        onUpdateDestinations={setDestinations}
      />

      {/* Stream Monitor Modal */}
      <StreamMonitor
        isOpen={showMonitor}
        onClose={() => setShowMonitor(false)}
        streamId={streamId}
        isLive={stream.isLive || false}
      />
    </div>
  );
}