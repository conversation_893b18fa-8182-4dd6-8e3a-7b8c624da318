/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(dashboard)/stream/[streamId]/page";
exports.ids = ["app/(dashboard)/stream/[streamId]/page"];
exports.modules = {

/***/ "(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227fa52424d0163cf1cd2ce3caeb4d80898fc4a86fa2%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fbd533db665dcb33fa445410fa7bbfafaebf88541%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227ff42123de51173081d4b4ef63aa3b7740b06543d3%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%2C%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fserver-actions.js%22%2C%5B%7B%22id%22%3A%227fb97b6ec16161b2c54cbe6be115c3e57262733006%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227fa52424d0163cf1cd2ce3caeb4d80898fc4a86fa2%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fbd533db665dcb33fa445410fa7bbfafaebf88541%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227ff42123de51173081d4b4ef63aa3b7740b06543d3%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%2C%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fserver-actions.js%22%2C%5B%7B%22id%22%3A%227fb97b6ec16161b2c54cbe6be115c3e57262733006%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"7fa52424d0163cf1cd2ce3caeb4d80898fc4a86fa2\": () => (/* reexport safe */ _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.syncKeylessConfigAction),\n/* harmony export */   \"7fb97b6ec16161b2c54cbe6be115c3e57262733006\": () => (/* reexport safe */ _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_server_actions_js__WEBPACK_IMPORTED_MODULE_1__.invalidateCacheAction),\n/* harmony export */   \"7fbd533db665dcb33fa445410fa7bbfafaebf88541\": () => (/* reexport safe */ _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.deleteKeylessAction),\n/* harmony export */   \"7ff42123de51173081d4b4ef63aa3b7740b06543d3\": () => (/* reexport safe */ _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__.createOrReadKeylessAction)\n/* harmony export */ });\n/* harmony import */ var _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_keyless_actions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js\");\n/* harmony import */ var _home_nick_streamyard_clonez_node_modules_clerk_nextjs_dist_esm_app_router_server_actions_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js */ \"(action-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fkeyless-actions.js%22%2C%5B%7B%22id%22%3A%227fa52424d0163cf1cd2ce3caeb4d80898fc4a86fa2%22%2C%22exportedName%22%3A%22syncKeylessConfigAction%22%7D%2C%7B%22id%22%3A%227fbd533db665dcb33fa445410fa7bbfafaebf88541%22%2C%22exportedName%22%3A%22deleteKeylessAction%22%7D%2C%7B%22id%22%3A%227ff42123de51173081d4b4ef63aa3b7740b06543d3%22%2C%22exportedName%22%3A%22createOrReadKeylessAction%22%7D%5D%5D%2C%5B%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fserver-actions.js%22%2C%5B%7B%22id%22%3A%227fb97b6ec16161b2c54cbe6be115c3e57262733006%22%2C%22exportedName%22%3A%22invalidateCacheAction%22%7D%5D%5D%5D&__client_imported__=true!\n");

/***/ }),

/***/ "(rsc)/./app/(dashboard)/stream/[streamId]/page.tsx":
/*!****************************************************!*\
  !*** ./app/(dashboard)/stream/[streamId]/page.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/home/<USER>/streamyard-clonez/app/(dashboard)/stream/[streamId]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/app/(dashboard)/stream/[streamId]/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"181dadd351d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9ob21lL25pY2svc3RyZWFteWFyZC1jbG9uZXovYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTgxZGFkZDM1MWQ5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_convex_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/convex-provider */ \"(rsc)/./components/convex-provider.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_livepeer_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/livepeer-provider */ \"(rsc)/./components/livepeer-provider.tsx\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./components/ui/sonner.tsx\");\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/header */ \"(rsc)/./components/header.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className),\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_convex_provider__WEBPACK_IMPORTED_MODULE_2__.ConvexClientProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_livepeer_provider__WEBPACK_IMPORTED_MODULE_4__.LivepeerProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                        attribute: \"class\",\n                        defaultTheme: \"dark\",\n                        enableSystem: false,\n                        storageKey: \"streamyard-clone-theme\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                                theme: \"light\",\n                                position: \"bottom-center\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_6__.Header, {}, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, this),\n                            children\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/app/layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/convex-provider.tsx":
/*!****************************************!*\
  !*** ./components/convex-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConvexClientProvider: () => (/* binding */ ConvexClientProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ConvexClientProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ConvexClientProvider() from the server but ConvexClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/components/convex-provider.tsx",
"ConvexClientProvider",
);

/***/ }),

/***/ "(rsc)/./components/header.tsx":
/*!*******************************!*\
  !*** ./components/header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/components/header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./components/livepeer-provider.tsx":
/*!******************************************!*\
  !*** ./components/livepeer-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LivepeerProvider: () => (/* binding */ LivepeerProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LivepeerProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LivepeerProvider() from the server but LivepeerProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/components/livepeer-provider.tsx",
"LivepeerProvider",
);

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/components/theme-provider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/home/<USER>/streamyard-clonez/components/ui/sonner.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&page=%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&appPaths=%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage.tsx&appDir=%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fnick%2Fstreamyard-clonez&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&page=%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&appPaths=%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage.tsx&appDir=%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fnick%2Fstreamyard-clonez&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/stream/[streamId]/page.tsx */ \"(rsc)/./app/(dashboard)/stream/[streamId]/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(dashboard)',\n        {\n        children: [\n        'stream',\n        {\n        children: [\n        '[streamId]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page7, \"/home/<USER>/streamyard-clonez/app/(dashboard)/stream/[streamId]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'not-found': [module4, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module5, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module6, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/home/<USER>/streamyard-clonez/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/streamyard-clonez/app/(dashboard)/stream/[streamId]/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(dashboard)/stream/[streamId]/page\",\n        pathname: \"/stream/[streamId]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&page=%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&appPaths=%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage.tsx&appDir=%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fnick%2Fstreamyard-clonez&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/stream/[streamId]/page.tsx */ \"(rsc)/./app/(dashboard)/stream/[streamId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZuaWNrJTJGc3RyZWFteWFyZC1jbG9uZXolMkZhcHAlMkYoZGFzaGJvYXJkKSUyRnN0cmVhbSUyRiU1QnN0cmVhbUlkJTVEJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUE0RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9hcHAvKGRhc2hib2FyZCkvc3RyZWFtL1tzdHJlYW1JZF0vcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/convex-provider.tsx */ \"(rsc)/./components/convex-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/header.tsx */ \"(rsc)/./components/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/livepeer-provider.tsx */ \"(rsc)/./components/livepeer-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(rsc)/./components/ui/sonner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZuaWNrJTJGc3RyZWFteWFyZC1jbG9uZXolMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZob21lJTJGbmljayUyRnN0cmVhbXlhcmQtY2xvbmV6JTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGaG9tZSUyRm5pY2slMkZzdHJlYW15YXJkLWNsb25leiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZuaWNrJTJGc3RyZWFteWFyZC1jbG9uZXolMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZodHRwLWFjY2Vzcy1mYWxsYmFjayUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZuaWNrJTJGc3RyZWFteWFyZC1jbG9uZXolMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZuaWNrJTJGc3RyZWFteWFyZC1jbG9uZXolMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZuaWNrJTJGc3RyZWFteWFyZC1jbG9uZXolMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRm1ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZuaWNrJTJGc3RyZWFteWFyZC1jbG9uZXolMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBeUg7QUFDekg7QUFDQSwwT0FBNEg7QUFDNUg7QUFDQSwwT0FBNEg7QUFDNUg7QUFDQSxvUkFBaUo7QUFDako7QUFDQSx3T0FBMkg7QUFDM0g7QUFDQSw0UEFBcUk7QUFDckk7QUFDQSxrUUFBd0k7QUFDeEk7QUFDQSxzUUFBMEkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL25pY2svc3RyZWFteWFyZC1jbG9uZXovbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9uaWNrL3N0cmVhbXlhcmQtY2xvbmV6L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL25pY2svc3RyZWFteWFyZC1jbG9uZXovbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9odHRwLWFjY2Vzcy1mYWxsYmFjay9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL25pY2svc3RyZWFteWFyZC1jbG9uZXovbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9tZXRhZGF0YS9hc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL21ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvaG9tZS9uaWNrL3N0cmVhbXlhcmQtY2xvbmV6L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/(dashboard)/stream/[streamId]/page.tsx":
/*!****************************************************!*\
  !*** ./app/(dashboard)/stream/[streamId]/page.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StreamPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/clerk-react/dist/index.mjs\");\n/* harmony import */ var _components_studio_StreamStudio__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/studio/StreamStudio */ \"(ssr)/./components/studio/StreamStudio.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction StreamPage() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const streamId = params.streamId;\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Please sign in to access the stream.\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/streamyard-clonez/app/(dashboard)/stream/[streamId]/page.tsx\",\n            lineNumber: 13,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_studio_StreamStudio__WEBPACK_IMPORTED_MODULE_2__.StreamStudio, {\n        streamId: streamId,\n        currentUserId: user.id\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/app/(dashboard)/stream/[streamId]/page.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvKGRhc2hib2FyZCkvc3RyZWFtL1tzdHJlYW1JZF0vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0QztBQUNKO0FBQ3dCO0FBRWpELFNBQVNHO0lBQ3RCLE1BQU1DLFNBQVNKLDBEQUFTQTtJQUN4QixNQUFNSyxXQUFXRCxPQUFPQyxRQUFRO0lBQ2hDLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdMLHNEQUFPQTtJQUV4QixJQUFJLENBQUNLLE1BQU07UUFDVCxxQkFBTyw4REFBQ0M7c0JBQUk7Ozs7OztJQUNkO0lBRUEscUJBQ0UsOERBQUNMLHlFQUFZQTtRQUNYRyxVQUFVQTtRQUNWRyxlQUFlRixLQUFLRyxFQUFFOzs7Ozs7QUFHNUIiLCJzb3VyY2VzIjpbIi9ob21lL25pY2svc3RyZWFteWFyZC1jbG9uZXovYXBwLyhkYXNoYm9hcmQpL3N0cmVhbS9bc3RyZWFtSWRdL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyB1c2VQYXJhbXMgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5pbXBvcnQgeyB1c2VVc2VyIH0gZnJvbSBcIkBjbGVyay9uZXh0anNcIjtcbmltcG9ydCB7IFN0cmVhbVN0dWRpbyB9IGZyb20gXCJAL2NvbXBvbmVudHMvc3R1ZGlvL1N0cmVhbVN0dWRpb1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTdHJlYW1QYWdlKCkge1xuICBjb25zdCBwYXJhbXMgPSB1c2VQYXJhbXMoKTtcbiAgY29uc3Qgc3RyZWFtSWQgPSBwYXJhbXMuc3RyZWFtSWQgYXMgc3RyaW5nO1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZVVzZXIoKTtcblxuICBpZiAoIXVzZXIpIHtcbiAgICByZXR1cm4gPGRpdj5QbGVhc2Ugc2lnbiBpbiB0byBhY2Nlc3MgdGhlIHN0cmVhbS48L2Rpdj47XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxTdHJlYW1TdHVkaW8gXG4gICAgICBzdHJlYW1JZD17c3RyZWFtSWQgYXMgYW55fSBcbiAgICAgIGN1cnJlbnRVc2VySWQ9e3VzZXIuaWR9XG4gICAgLz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VQYXJhbXMiLCJ1c2VVc2VyIiwiU3RyZWFtU3R1ZGlvIiwiU3RyZWFtUGFnZSIsInBhcmFtcyIsInN0cmVhbUlkIiwidXNlciIsImRpdiIsImN1cnJlbnRVc2VySWQiLCJpZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/(dashboard)/stream/[streamId]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/convex-provider.tsx":
/*!****************************************!*\
  !*** ./components/convex-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvexClientProvider: () => (/* binding */ ConvexClientProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var convex_react_clerk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react-clerk */ \"(ssr)/./node_modules/convex/dist/esm/react-clerk/index.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js\");\n/* __next_internal_client_entry_do_not_use__ ConvexClientProvider auto */ \n\n\n\nconst convex = new convex_react__WEBPACK_IMPORTED_MODULE_1__.ConvexReactClient(\"https://wonderful-kangaroo-238.convex.cloud\");\nfunction ConvexClientProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.ClerkProvider, {\n        publishableKey: \"pk_test_Z2l2aW5nLXNrdW5rLTMxLmNsZXJrLmFjY291bnRzLmRldiQ\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(convex_react_clerk__WEBPACK_IMPORTED_MODULE_2__.ConvexProviderWithClerk, {\n            client: convex,\n            useAuth: _clerk_nextjs__WEBPACK_IMPORTED_MODULE_4__.usePromisifiedAuth,\n            children: children\n        }, void 0, false, {\n            fileName: \"/home/<USER>/streamyard-clonez/components/convex-provider.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/convex-provider.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2NvbnZleC1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFaUQ7QUFDWTtBQUNOO0FBRXZELE1BQU1JLFNBQVMsSUFBSUosMkRBQWlCQSxDQUFDSyw2Q0FBa0M7QUFFaEUsU0FBU0cscUJBQXFCLEVBQ25DQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ1Asd0RBQWFBO1FBQUNRLGdCQUFnQkwseURBQTZDO2tCQUMxRSw0RUFBQ0osdUVBQXVCQTtZQUFDVyxRQUFRUjtZQUFRRCxTQUFTQSw2REFBT0E7c0JBQ3RETTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9jb21wb25lbnRzL2NvbnZleC1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IENvbnZleFJlYWN0Q2xpZW50IH0gZnJvbSBcImNvbnZleC9yZWFjdFwiO1xuaW1wb3J0IHsgQ29udmV4UHJvdmlkZXJXaXRoQ2xlcmsgfSBmcm9tIFwiY29udmV4L3JlYWN0LWNsZXJrXCI7XG5pbXBvcnQgeyBDbGVya1Byb3ZpZGVyLCB1c2VBdXRoIH0gZnJvbSBcIkBjbGVyay9uZXh0anNcIjtcblxuY29uc3QgY29udmV4ID0gbmV3IENvbnZleFJlYWN0Q2xpZW50KHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NPTlZFWF9VUkwhKTtcblxuZXhwb3J0IGZ1bmN0aW9uIENvbnZleENsaWVudFByb3ZpZGVyKHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPENsZXJrUHJvdmlkZXIgcHVibGlzaGFibGVLZXk9e3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0NMRVJLX1BVQkxJU0hBQkxFX0tFWSF9PlxuICAgICAgPENvbnZleFByb3ZpZGVyV2l0aENsZXJrIGNsaWVudD17Y29udmV4fSB1c2VBdXRoPXt1c2VBdXRofT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9Db252ZXhQcm92aWRlcldpdGhDbGVyaz5cbiAgICA8L0NsZXJrUHJvdmlkZXI+XG4gICk7XG59Il0sIm5hbWVzIjpbIkNvbnZleFJlYWN0Q2xpZW50IiwiQ29udmV4UHJvdmlkZXJXaXRoQ2xlcmsiLCJDbGVya1Byb3ZpZGVyIiwidXNlQXV0aCIsImNvbnZleCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19DT05WRVhfVVJMIiwiQ29udmV4Q2xpZW50UHJvdmlkZXIiLCJjaGlsZHJlbiIsInB1Ymxpc2hhYmxlS2V5IiwiTkVYVF9QVUJMSUNfQ0xFUktfUFVCTElTSEFCTEVfS0VZIiwiY2xpZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/convex-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/header.tsx":
/*!*******************************!*\
  !*** ./components/header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/./node_modules/@clerk/clerk-react/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\nfunction Header() {\n    const { isSignedIn } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container flex h-14 items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-4 flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"mr-6 flex items-center space-x-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-bold\",\n                            children: \"StreamYard Clone\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 items-center justify-between space-x-2 md:justify-end\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex items-center space-x-6\",\n                            children: isSignedIn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/create\",\n                                    className: \"text-sm font-medium transition-colors hover:text-primary\",\n                                    children: \"Create Stream\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: isSignedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.UserButton, {\n                                afterSignOutUrl: \"/\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/sign-in\",\n                                className: \"text-sm font-medium transition-colors hover:text-primary\",\n                                children: \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/header.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/livepeer-provider.tsx":
/*!******************************************!*\
  !*** ./components/livepeer-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LivepeerProvider: () => (/* binding */ LivepeerProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ LivepeerProvider auto */ \nfunction LivepeerProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2xpdmVwZWVyLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRU8sU0FBU0EsaUJBQWlCLEVBQy9CQyxRQUFRLEVBR1Q7SUFDQyxxQkFBTztrQkFBR0E7O0FBQ1oiLCJzb3VyY2VzIjpbIi9ob21lL25pY2svc3RyZWFteWFyZC1jbG9uZXovY29tcG9uZW50cy9saXZlcGVlci1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBMaXZlcGVlclByb3ZpZGVyKHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz47XG59Il0sIm5hbWVzIjpbIkxpdmVwZWVyUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/livepeer-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/moderation/BreakoutRoomManager.tsx":
/*!*******************************************************!*\
  !*** ./components/moderation/BreakoutRoomManager.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BreakoutRoomManager: () => (/* binding */ BreakoutRoomManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BreakoutRoomManager auto */ \n\nfunction BreakoutRoomManager({ streamId, currentUserId }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-semibold mb-4\",\n                children: \"Breakout Room Manager\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/BreakoutRoomManager.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Breakout room features coming soon...\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/BreakoutRoomManager.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/BreakoutRoomManager.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL21vZGVyYXRpb24vQnJlYWtvdXRSb29tTWFuYWdlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTBCO0FBUW5CLFNBQVNDLG9CQUFvQixFQUFFQyxRQUFRLEVBQUVDLGFBQWEsRUFBNEI7SUFDdkYscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBNkI7Ozs7OzswQkFDM0MsOERBQUNFO2dCQUFFRixXQUFVOzBCQUFnQjs7Ozs7Ozs7Ozs7O0FBR25DIiwic291cmNlcyI6WyIvaG9tZS9uaWNrL3N0cmVhbXlhcmQtY2xvbmV6L2NvbXBvbmVudHMvbW9kZXJhdGlvbi9CcmVha291dFJvb21NYW5hZ2VyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IElkIH0gZnJvbSAnLi4vLi4vY29udmV4L19nZW5lcmF0ZWQvZGF0YU1vZGVsJztcblxuaW50ZXJmYWNlIEJyZWFrb3V0Um9vbU1hbmFnZXJQcm9wcyB7XG4gIHN0cmVhbUlkOiBJZDxcInN0cmVhbXNcIj47XG4gIGN1cnJlbnRVc2VySWQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEJyZWFrb3V0Um9vbU1hbmFnZXIoeyBzdHJlYW1JZCwgY3VycmVudFVzZXJJZCB9OiBCcmVha291dFJvb21NYW5hZ2VyUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi00XCI+QnJlYWtvdXQgUm9vbSBNYW5hZ2VyPC9oMj5cbiAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5CcmVha291dCByb29tIGZlYXR1cmVzIGNvbWluZyBzb29uLi4uPC9wPlxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJSZWFjdCIsIkJyZWFrb3V0Um9vbU1hbmFnZXIiLCJzdHJlYW1JZCIsImN1cnJlbnRVc2VySWQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/moderation/BreakoutRoomManager.tsx\n");

/***/ }),

/***/ "(ssr)/./components/moderation/ModerationPanel.tsx":
/*!***************************************************!*\
  !*** ./components/moderation/ModerationPanel.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModerationPanel: () => (/* binding */ ModerationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ModerationPanel auto */ \n\nfunction ModerationPanel({ streamId, currentUserId }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-semibold mb-4\",\n                children: \"Moderation Panel\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/ModerationPanel.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600\",\n                children: \"Moderation features coming soon...\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/ModerationPanel.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/ModerationPanel.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL21vZGVyYXRpb24vTW9kZXJhdGlvblBhbmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFMEI7QUFRbkIsU0FBU0MsZ0JBQWdCLEVBQUVDLFFBQVEsRUFBRUMsYUFBYSxFQUF3QjtJQUMvRSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUE2Qjs7Ozs7OzBCQUMzQyw4REFBQ0U7Z0JBQUVGLFdBQVU7MEJBQWdCOzs7Ozs7Ozs7Ozs7QUFHbkMiLCJzb3VyY2VzIjpbIi9ob21lL25pY2svc3RyZWFteWFyZC1jbG9uZXovY29tcG9uZW50cy9tb2RlcmF0aW9uL01vZGVyYXRpb25QYW5lbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBJZCB9IGZyb20gJy4uLy4uL2NvbnZleC9fZ2VuZXJhdGVkL2RhdGFNb2RlbCc7XG5cbmludGVyZmFjZSBNb2RlcmF0aW9uUGFuZWxQcm9wcyB7XG4gIHN0cmVhbUlkOiBJZDxcInN0cmVhbXNcIj47XG4gIGN1cnJlbnRVc2VySWQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIE1vZGVyYXRpb25QYW5lbCh7IHN0cmVhbUlkLCBjdXJyZW50VXNlcklkIH06IE1vZGVyYXRpb25QYW5lbFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdyBwLTZcIj5cbiAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItNFwiPk1vZGVyYXRpb24gUGFuZWw8L2gyPlxuICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPk1vZGVyYXRpb24gZmVhdHVyZXMgY29taW5nIHNvb24uLi48L3A+XG4gICAgPC9kaXY+XG4gICk7XG59Il0sIm5hbWVzIjpbIlJlYWN0IiwiTW9kZXJhdGlvblBhbmVsIiwic3RyZWFtSWQiLCJjdXJyZW50VXNlcklkIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJwIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/moderation/ModerationPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./components/moderation/PermissionControl.tsx":
/*!*****************************************************!*\
  !*** ./components/moderation/PermissionControl.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionControl: () => (/* binding */ PermissionControl)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../convex/_generated/api */ \"(ssr)/./convex/_generated/api.js\");\n/* __next_internal_client_entry_do_not_use__ PermissionControl auto */ \n\n\n\nfunction PermissionControl({ streamId, currentUserId }) {\n    const stream = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.streams.get, {\n        streamId\n    });\n    const participants = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.participants.getStreamParticipants, {\n        streamId\n    });\n    const updateStream = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.streams.updateStream);\n    const [globalSettings, setGlobalSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isChatEnabled: stream?.isChatEnabled ?? true,\n        isChatDelayed: stream?.isChatDelayed ?? false,\n        isChatFollowersOnly: stream?.isChatFollowersOnly ?? false\n    });\n    const handleGlobalSettingChange = async (setting, value)=>{\n        const newSettings = {\n            ...globalSettings,\n            [setting]: value\n        };\n        setGlobalSettings(newSettings);\n        try {\n            await updateStream({\n                streamId,\n                [setting]: value\n            });\n        } catch (error) {\n            console.error('Failed to update stream settings:', error);\n        }\n    };\n    const handleParticipantPermissionChange = async (userId, permission, value)=>{\n        try {\n            // This would update participant permissions\n            console.log('Updating participant permission:', {\n                userId,\n                permission,\n                value\n            });\n        } catch (error) {\n            console.error('Failed to update participant permissions:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4\",\n                children: \"Permission Control\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium mb-3\",\n                        children: \"Global Stream Settings\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"font-medium\",\n                                                children: \"Enable Chat\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Participants can send chat messages\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: globalSettings.isChatEnabled,\n                                                onChange: (e)=>handleGlobalSettingChange('isChatEnabled', e.target.checked),\n                                                className: \"sr-only peer\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"font-medium\",\n                                                children: \"Chat Delay\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Add delay to chat messages\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: globalSettings.isChatDelayed,\n                                                onChange: (e)=>handleGlobalSettingChange('isChatDelayed', e.target.checked),\n                                                className: \"sr-only peer\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"font-medium\",\n                                                children: \"Followers Only Chat\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Only followers can chat\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: globalSettings.isChatFollowersOnly,\n                                                onChange: (e)=>handleGlobalSettingChange('isChatFollowersOnly', e.target.checked),\n                                                className: \"sr-only peer\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium mb-3\",\n                        children: \"Individual Permissions\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: participants?.filter((p)=>p.role !== 'host').map((participant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium\",\n                                                    children: participant.user?.username || 'Anonymous'\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `text-xs px-2 py-1 rounded ${participant.role === 'moderator' ? 'bg-blue-100 text-blue-800' : participant.role === 'co-host' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'}`,\n                                                    children: participant.role\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Can Speak\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        defaultChecked: participant.role !== 'viewer',\n                                                        onChange: (e)=>handleParticipantPermissionChange(participant.userId, 'canSpeak', e.target.checked),\n                                                        className: \"rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Can Video\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        defaultChecked: participant.role !== 'viewer',\n                                                        onChange: (e)=>handleParticipantPermissionChange(participant.userId, 'canVideo', e.target.checked),\n                                                        className: \"rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Can Screen Share\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        defaultChecked: participant.role === 'host' || participant.role === 'co-host',\n                                                        onChange: (e)=>handleParticipantPermissionChange(participant.userId, 'canScreenShare', e.target.checked),\n                                                        className: \"rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm\",\n                                                        children: \"Can Chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        defaultChecked: true,\n                                                        onChange: (e)=>handleParticipantPermissionChange(participant.userId, 'canChat', e.target.checked),\n                                                        className: \"rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, participant._id, true, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/moderation/PermissionControl.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL21vZGVyYXRpb24vUGVybWlzc2lvbkNvbnRyb2wudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRXdDO0FBQ2E7QUFDSDtBQVEzQyxTQUFTSyxrQkFBa0IsRUFBRUMsUUFBUSxFQUFFQyxhQUFhLEVBQTBCO0lBQ25GLE1BQU1DLFNBQVNOLHNEQUFRQSxDQUFDRSxzREFBR0EsQ0FBQ0ssT0FBTyxDQUFDQyxHQUFHLEVBQUU7UUFBRUo7SUFBUztJQUNwRCxNQUFNSyxlQUFlVCxzREFBUUEsQ0FBQ0Usc0RBQUdBLENBQUNPLFlBQVksQ0FBQ0MscUJBQXFCLEVBQUU7UUFBRU47SUFBUztJQUNqRixNQUFNTyxlQUFlVix5REFBV0EsQ0FBQ0Msc0RBQUdBLENBQUNLLE9BQU8sQ0FBQ0ksWUFBWTtJQUV6RCxNQUFNLENBQUNDLGdCQUFnQkMsa0JBQWtCLEdBQUdkLCtDQUFRQSxDQUFDO1FBQ25EZSxlQUFlUixRQUFRUSxpQkFBaUI7UUFDeENDLGVBQWVULFFBQVFTLGlCQUFpQjtRQUN4Q0MscUJBQXFCVixRQUFRVSx1QkFBdUI7SUFDdEQ7SUFFQSxNQUFNQyw0QkFBNEIsT0FBT0MsU0FBc0NDO1FBQzdFLE1BQU1DLGNBQWM7WUFBRSxHQUFHUixjQUFjO1lBQUUsQ0FBQ00sUUFBUSxFQUFFQztRQUFNO1FBQzFETixrQkFBa0JPO1FBRWxCLElBQUk7WUFDRixNQUFNVCxhQUFhO2dCQUNqQlA7Z0JBQ0EsQ0FBQ2MsUUFBUSxFQUFFQztZQUNiO1FBQ0YsRUFBRSxPQUFPRSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxxQ0FBcUNBO1FBQ3JEO0lBQ0Y7SUFFQSxNQUFNRSxvQ0FBb0MsT0FDeENDLFFBQ0FDLFlBQ0FOO1FBRUEsSUFBSTtZQUNGLDRDQUE0QztZQUM1Q0csUUFBUUksR0FBRyxDQUFDLG9DQUFvQztnQkFBRUY7Z0JBQVFDO2dCQUFZTjtZQUFNO1FBQzlFLEVBQUUsT0FBT0UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNkNBQTZDQTtRQUM3RDtJQUNGO0lBRUEscUJBQ0UsOERBQUNNO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBNkI7Ozs7OzswQkFHM0MsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUdGLFdBQVU7a0NBQW1COzs7Ozs7a0NBQ2pDLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDSTtnREFBTUgsV0FBVTswREFBYzs7Ozs7OzBEQUMvQiw4REFBQ0k7Z0RBQUVKLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBRXZDLDhEQUFDRzt3Q0FBTUgsV0FBVTs7MERBQ2YsOERBQUNLO2dEQUNDQyxNQUFLO2dEQUNMQyxTQUFTdkIsZUFBZUUsYUFBYTtnREFDckNzQixVQUFVLENBQUNDLElBQU1wQiwwQkFBMEIsaUJBQWlCb0IsRUFBRUMsTUFBTSxDQUFDSCxPQUFPO2dEQUM1RVAsV0FBVTs7Ozs7OzBEQUVaLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUluQiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNJO2dEQUFNSCxXQUFVOzBEQUFjOzs7Ozs7MERBQy9CLDhEQUFDSTtnREFBRUosV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFFdkMsOERBQUNHO3dDQUFNSCxXQUFVOzswREFDZiw4REFBQ0s7Z0RBQ0NDLE1BQUs7Z0RBQ0xDLFNBQVN2QixlQUFlRyxhQUFhO2dEQUNyQ3FCLFVBQVUsQ0FBQ0MsSUFBTXBCLDBCQUEwQixpQkFBaUJvQixFQUFFQyxNQUFNLENBQUNILE9BQU87Z0RBQzVFUCxXQUFVOzs7Ozs7MERBRVosOERBQUNEO2dEQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSW5CLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEOzswREFDQyw4REFBQ0k7Z0RBQU1ILFdBQVU7MERBQWM7Ozs7OzswREFDL0IsOERBQUNJO2dEQUFFSixXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUV2Qyw4REFBQ0c7d0NBQU1ILFdBQVU7OzBEQUNmLDhEQUFDSztnREFDQ0MsTUFBSztnREFDTEMsU0FBU3ZCLGVBQWVJLG1CQUFtQjtnREFDM0NvQixVQUFVLENBQUNDLElBQU1wQiwwQkFBMEIsdUJBQXVCb0IsRUFBRUMsTUFBTSxDQUFDSCxPQUFPO2dEQUNsRlAsV0FBVTs7Ozs7OzBEQUVaLDhEQUFDRDtnREFBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU92Qiw4REFBQ0Q7O2tDQUNDLDhEQUFDRzt3QkFBR0YsV0FBVTtrQ0FBbUI7Ozs7OztrQ0FDakMsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNabkIsY0FBYzhCLE9BQU9QLENBQUFBLElBQUtBLEVBQUVRLElBQUksS0FBSyxRQUFRQyxJQUFJLENBQUNDLDRCQUNqRCw4REFBQ2Y7Z0NBQTBCQyxXQUFVOztrREFDbkMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs7OERBQ0MsOERBQUNnQjtvREFBR2YsV0FBVTs4REFBZWMsWUFBWUUsSUFBSSxFQUFFQyxZQUFZOzs7Ozs7OERBQzNELDhEQUFDQztvREFBS2xCLFdBQVcsQ0FBQywwQkFBMEIsRUFDMUNjLFlBQVlGLElBQUksS0FBSyxjQUFjLDhCQUNuQ0UsWUFBWUYsSUFBSSxLQUFLLFlBQVksa0NBQ2pDLDZCQUNBOzhEQUNDRSxZQUFZRixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OztrREFLdkIsOERBQUNiO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDa0I7d0RBQUtsQixXQUFVO2tFQUFVOzs7Ozs7a0VBQzFCLDhEQUFDSzt3REFDQ0MsTUFBSzt3REFDTGEsZ0JBQWdCTCxZQUFZRixJQUFJLEtBQUs7d0RBQ3JDSixVQUFVLENBQUNDLElBQU1kLGtDQUNmbUIsWUFBWWxCLE1BQU0sRUFDbEIsWUFDQWEsRUFBRUMsTUFBTSxDQUFDSCxPQUFPO3dEQUVsQlAsV0FBVTs7Ozs7Ozs7Ozs7OzBEQUlkLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNrQjt3REFBS2xCLFdBQVU7a0VBQVU7Ozs7OztrRUFDMUIsOERBQUNLO3dEQUNDQyxNQUFLO3dEQUNMYSxnQkFBZ0JMLFlBQVlGLElBQUksS0FBSzt3REFDckNKLFVBQVUsQ0FBQ0MsSUFBTWQsa0NBQ2ZtQixZQUFZbEIsTUFBTSxFQUNsQixZQUNBYSxFQUFFQyxNQUFNLENBQUNILE9BQU87d0RBRWxCUCxXQUFVOzs7Ozs7Ozs7Ozs7MERBSWQsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2tCO3dEQUFLbEIsV0FBVTtrRUFBVTs7Ozs7O2tFQUMxQiw4REFBQ0s7d0RBQ0NDLE1BQUs7d0RBQ0xhLGdCQUFnQkwsWUFBWUYsSUFBSSxLQUFLLFVBQVVFLFlBQVlGLElBQUksS0FBSzt3REFDcEVKLFVBQVUsQ0FBQ0MsSUFBTWQsa0NBQ2ZtQixZQUFZbEIsTUFBTSxFQUNsQixrQkFDQWEsRUFBRUMsTUFBTSxDQUFDSCxPQUFPO3dEQUVsQlAsV0FBVTs7Ozs7Ozs7Ozs7OzBEQUlkLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNrQjt3REFBS2xCLFdBQVU7a0VBQVU7Ozs7OztrRUFDMUIsOERBQUNLO3dEQUNDQyxNQUFLO3dEQUNMYSxnQkFBZ0I7d0RBQ2hCWCxVQUFVLENBQUNDLElBQU1kLGtDQUNmbUIsWUFBWWxCLE1BQU0sRUFDbEIsV0FDQWEsRUFBRUMsTUFBTSxDQUFDSCxPQUFPO3dEQUVsQlAsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQkFuRVJjLFlBQVlNLEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUE2RXJDIiwic291cmNlcyI6WyIvaG9tZS9uaWNrL3N0cmVhbXlhcmQtY2xvbmV6L2NvbXBvbmVudHMvbW9kZXJhdGlvbi9QZXJtaXNzaW9uQ29udHJvbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVF1ZXJ5LCB1c2VNdXRhdGlvbiB9IGZyb20gJ2NvbnZleC9yZWFjdCc7XG5pbXBvcnQgeyBhcGkgfSBmcm9tICcuLi8uLi9jb252ZXgvX2dlbmVyYXRlZC9hcGknO1xuaW1wb3J0IHsgSWQgfSBmcm9tICcuLi8uLi9jb252ZXgvX2dlbmVyYXRlZC9kYXRhTW9kZWwnO1xuXG5pbnRlcmZhY2UgUGVybWlzc2lvbkNvbnRyb2xQcm9wcyB7XG4gIHN0cmVhbUlkOiBJZDxcInN0cmVhbXNcIj47XG4gIGN1cnJlbnRVc2VySWQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFBlcm1pc3Npb25Db250cm9sKHsgc3RyZWFtSWQsIGN1cnJlbnRVc2VySWQgfTogUGVybWlzc2lvbkNvbnRyb2xQcm9wcykge1xuICBjb25zdCBzdHJlYW0gPSB1c2VRdWVyeShhcGkuc3RyZWFtcy5nZXQsIHsgc3RyZWFtSWQgfSk7XG4gIGNvbnN0IHBhcnRpY2lwYW50cyA9IHVzZVF1ZXJ5KGFwaS5wYXJ0aWNpcGFudHMuZ2V0U3RyZWFtUGFydGljaXBhbnRzLCB7IHN0cmVhbUlkIH0pO1xuICBjb25zdCB1cGRhdGVTdHJlYW0gPSB1c2VNdXRhdGlvbihhcGkuc3RyZWFtcy51cGRhdGVTdHJlYW0pO1xuXG4gIGNvbnN0IFtnbG9iYWxTZXR0aW5ncywgc2V0R2xvYmFsU2V0dGluZ3NdID0gdXNlU3RhdGUoe1xuICAgIGlzQ2hhdEVuYWJsZWQ6IHN0cmVhbT8uaXNDaGF0RW5hYmxlZCA/PyB0cnVlLFxuICAgIGlzQ2hhdERlbGF5ZWQ6IHN0cmVhbT8uaXNDaGF0RGVsYXllZCA/PyBmYWxzZSxcbiAgICBpc0NoYXRGb2xsb3dlcnNPbmx5OiBzdHJlYW0/LmlzQ2hhdEZvbGxvd2Vyc09ubHkgPz8gZmFsc2UsXG4gIH0pO1xuXG4gIGNvbnN0IGhhbmRsZUdsb2JhbFNldHRpbmdDaGFuZ2UgPSBhc3luYyAoc2V0dGluZzoga2V5b2YgdHlwZW9mIGdsb2JhbFNldHRpbmdzLCB2YWx1ZTogYm9vbGVhbikgPT4ge1xuICAgIGNvbnN0IG5ld1NldHRpbmdzID0geyAuLi5nbG9iYWxTZXR0aW5ncywgW3NldHRpbmddOiB2YWx1ZSB9O1xuICAgIHNldEdsb2JhbFNldHRpbmdzKG5ld1NldHRpbmdzKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgYXdhaXQgdXBkYXRlU3RyZWFtKHtcbiAgICAgICAgc3RyZWFtSWQsXG4gICAgICAgIFtzZXR0aW5nXTogdmFsdWUsXG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHVwZGF0ZSBzdHJlYW0gc2V0dGluZ3M6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVQYXJ0aWNpcGFudFBlcm1pc3Npb25DaGFuZ2UgPSBhc3luYyAoXG4gICAgdXNlcklkOiBzdHJpbmcsXG4gICAgcGVybWlzc2lvbjogc3RyaW5nLFxuICAgIHZhbHVlOiBib29sZWFuXG4gICkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBUaGlzIHdvdWxkIHVwZGF0ZSBwYXJ0aWNpcGFudCBwZXJtaXNzaW9uc1xuICAgICAgY29uc29sZS5sb2coJ1VwZGF0aW5nIHBhcnRpY2lwYW50IHBlcm1pc3Npb246JywgeyB1c2VySWQsIHBlcm1pc3Npb24sIHZhbHVlIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIHBhcnRpY2lwYW50IHBlcm1pc3Npb25zOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93IHAtNlwiPlxuICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi00XCI+UGVybWlzc2lvbiBDb250cm9sPC9oMz5cbiAgICAgIFxuICAgICAgey8qIEdsb2JhbCBTdHJlYW0gU2V0dGluZ3MgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIG1iLTNcIj5HbG9iYWwgU3RyZWFtIFNldHRpbmdzPC9oND5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RW5hYmxlIENoYXQ8L2xhYmVsPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5QYXJ0aWNpcGFudHMgY2FuIHNlbmQgY2hhdCBtZXNzYWdlczwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInJlbGF0aXZlIGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2dsb2JhbFNldHRpbmdzLmlzQ2hhdEVuYWJsZWR9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVHbG9iYWxTZXR0aW5nQ2hhbmdlKCdpc0NoYXRFbmFibGVkJywgZS50YXJnZXQuY2hlY2tlZCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic3Itb25seSBwZWVyXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTExIGgtNiBiZy1ncmF5LTIwMCBwZWVyLWZvY3VzOm91dGxpbmUtbm9uZSBwZWVyLWZvY3VzOnJpbmctNCBwZWVyLWZvY3VzOnJpbmctYmx1ZS0zMDAgcm91bmRlZC1mdWxsIHBlZXIgcGVlci1jaGVja2VkOmFmdGVyOnRyYW5zbGF0ZS14LWZ1bGwgcGVlci1jaGVja2VkOmFmdGVyOmJvcmRlci13aGl0ZSBhZnRlcjpjb250ZW50LVsnJ10gYWZ0ZXI6YWJzb2x1dGUgYWZ0ZXI6dG9wLVsycHhdIGFmdGVyOmxlZnQtWzJweF0gYWZ0ZXI6Ymctd2hpdGUgYWZ0ZXI6Ym9yZGVyLWdyYXktMzAwIGFmdGVyOmJvcmRlciBhZnRlcjpyb3VuZGVkLWZ1bGwgYWZ0ZXI6aC01IGFmdGVyOnctNSBhZnRlcjp0cmFuc2l0aW9uLWFsbCBwZWVyLWNoZWNrZWQ6YmctYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+Q2hhdCBEZWxheTwvbGFiZWw+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPkFkZCBkZWxheSB0byBjaGF0IG1lc3NhZ2VzPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwicmVsYXRpdmUgaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgY2hlY2tlZD17Z2xvYmFsU2V0dGluZ3MuaXNDaGF0RGVsYXllZH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUdsb2JhbFNldHRpbmdDaGFuZ2UoJ2lzQ2hhdERlbGF5ZWQnLCBlLnRhcmdldC5jaGVja2VkKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzci1vbmx5IHBlZXJcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTEgaC02IGJnLWdyYXktMjAwIHBlZXItZm9jdXM6b3V0bGluZS1ub25lIHBlZXItZm9jdXM6cmluZy00IHBlZXItZm9jdXM6cmluZy1ibHVlLTMwMCByb3VuZGVkLWZ1bGwgcGVlciBwZWVyLWNoZWNrZWQ6YWZ0ZXI6dHJhbnNsYXRlLXgtZnVsbCBwZWVyLWNoZWNrZWQ6YWZ0ZXI6Ym9yZGVyLXdoaXRlIGFmdGVyOmNvbnRlbnQtWycnXSBhZnRlcjphYnNvbHV0ZSBhZnRlcjp0b3AtWzJweF0gYWZ0ZXI6bGVmdC1bMnB4XSBhZnRlcjpiZy13aGl0ZSBhZnRlcjpib3JkZXItZ3JheS0zMDAgYWZ0ZXI6Ym9yZGVyIGFmdGVyOnJvdW5kZWQtZnVsbCBhZnRlcjpoLTUgYWZ0ZXI6dy01IGFmdGVyOnRyYW5zaXRpb24tYWxsIHBlZXItY2hlY2tlZDpiZy1ibHVlLTYwMFwiPjwvZGl2PlxuICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5Gb2xsb3dlcnMgT25seSBDaGF0PC9sYWJlbD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+T25seSBmb2xsb3dlcnMgY2FuIGNoYXQ8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJyZWxhdGl2ZSBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICBjaGVja2VkPXtnbG9iYWxTZXR0aW5ncy5pc0NoYXRGb2xsb3dlcnNPbmx5fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlR2xvYmFsU2V0dGluZ0NoYW5nZSgnaXNDaGF0Rm9sbG93ZXJzT25seScsIGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNyLW9ubHkgcGVlclwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMSBoLTYgYmctZ3JheS0yMDAgcGVlci1mb2N1czpvdXRsaW5lLW5vbmUgcGVlci1mb2N1czpyaW5nLTQgcGVlci1mb2N1czpyaW5nLWJsdWUtMzAwIHJvdW5kZWQtZnVsbCBwZWVyIHBlZXItY2hlY2tlZDphZnRlcjp0cmFuc2xhdGUteC1mdWxsIHBlZXItY2hlY2tlZDphZnRlcjpib3JkZXItd2hpdGUgYWZ0ZXI6Y29udGVudC1bJyddIGFmdGVyOmFic29sdXRlIGFmdGVyOnRvcC1bMnB4XSBhZnRlcjpsZWZ0LVsycHhdIGFmdGVyOmJnLXdoaXRlIGFmdGVyOmJvcmRlci1ncmF5LTMwMCBhZnRlcjpib3JkZXIgYWZ0ZXI6cm91bmRlZC1mdWxsIGFmdGVyOmgtNSBhZnRlcjp3LTUgYWZ0ZXI6dHJhbnNpdGlvbi1hbGwgcGVlci1jaGVja2VkOmJnLWJsdWUtNjAwXCI+PC9kaXY+XG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogSW5kaXZpZHVhbCBQYXJ0aWNpcGFudCBQZXJtaXNzaW9ucyAqL31cbiAgICAgIDxkaXY+XG4gICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSBtYi0zXCI+SW5kaXZpZHVhbCBQZXJtaXNzaW9uczwvaDQ+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAge3BhcnRpY2lwYW50cz8uZmlsdGVyKHAgPT4gcC5yb2xlICE9PSAnaG9zdCcpLm1hcCgocGFydGljaXBhbnQpID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtwYXJ0aWNpcGFudC5faWR9IGNsYXNzTmFtZT1cImJvcmRlciByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntwYXJ0aWNpcGFudC51c2VyPy51c2VybmFtZSB8fCAnQW5vbnltb3VzJ308L2g1PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC14cyBweC0yIHB5LTEgcm91bmRlZCAke1xuICAgICAgICAgICAgICAgICAgICBwYXJ0aWNpcGFudC5yb2xlID09PSAnbW9kZXJhdG9yJyA/ICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJyA6XG4gICAgICAgICAgICAgICAgICAgIHBhcnRpY2lwYW50LnJvbGUgPT09ICdjby1ob3N0JyA/ICdiZy1wdXJwbGUtMTAwIHRleHQtcHVycGxlLTgwMCcgOlxuICAgICAgICAgICAgICAgICAgICAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge3BhcnRpY2lwYW50LnJvbGV9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5DYW4gU3BlYWs8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgZGVmYXVsdENoZWNrZWQ9e3BhcnRpY2lwYW50LnJvbGUgIT09ICd2aWV3ZXInfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVBhcnRpY2lwYW50UGVybWlzc2lvbkNoYW5nZShcbiAgICAgICAgICAgICAgICAgICAgICBwYXJ0aWNpcGFudC51c2VySWQsIFxuICAgICAgICAgICAgICAgICAgICAgICdjYW5TcGVhaycsIFxuICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LmNoZWNrZWRcbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+Q2FuIFZpZGVvPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgIGRlZmF1bHRDaGVja2VkPXtwYXJ0aWNpcGFudC5yb2xlICE9PSAndmlld2VyJ31cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVQYXJ0aWNpcGFudFBlcm1pc3Npb25DaGFuZ2UoXG4gICAgICAgICAgICAgICAgICAgICAgcGFydGljaXBhbnQudXNlcklkLCBcbiAgICAgICAgICAgICAgICAgICAgICAnY2FuVmlkZW8nLCBcbiAgICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC5jaGVja2VkXG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPkNhbiBTY3JlZW4gU2hhcmU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgZGVmYXVsdENoZWNrZWQ9e3BhcnRpY2lwYW50LnJvbGUgPT09ICdob3N0JyB8fCBwYXJ0aWNpcGFudC5yb2xlID09PSAnY28taG9zdCd9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlUGFydGljaXBhbnRQZXJtaXNzaW9uQ2hhbmdlKFxuICAgICAgICAgICAgICAgICAgICAgIHBhcnRpY2lwYW50LnVzZXJJZCwgXG4gICAgICAgICAgICAgICAgICAgICAgJ2NhblNjcmVlblNoYXJlJywgXG4gICAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQuY2hlY2tlZFxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc21cIj5DYW4gQ2hhdDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICBkZWZhdWx0Q2hlY2tlZD17dHJ1ZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVQYXJ0aWNpcGFudFBlcm1pc3Npb25DaGFuZ2UoXG4gICAgICAgICAgICAgICAgICAgICAgcGFydGljaXBhbnQudXNlcklkLCBcbiAgICAgICAgICAgICAgICAgICAgICAnY2FuQ2hhdCcsIFxuICAgICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LmNoZWNrZWRcbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUXVlcnkiLCJ1c2VNdXRhdGlvbiIsImFwaSIsIlBlcm1pc3Npb25Db250cm9sIiwic3RyZWFtSWQiLCJjdXJyZW50VXNlcklkIiwic3RyZWFtIiwic3RyZWFtcyIsImdldCIsInBhcnRpY2lwYW50cyIsImdldFN0cmVhbVBhcnRpY2lwYW50cyIsInVwZGF0ZVN0cmVhbSIsImdsb2JhbFNldHRpbmdzIiwic2V0R2xvYmFsU2V0dGluZ3MiLCJpc0NoYXRFbmFibGVkIiwiaXNDaGF0RGVsYXllZCIsImlzQ2hhdEZvbGxvd2Vyc09ubHkiLCJoYW5kbGVHbG9iYWxTZXR0aW5nQ2hhbmdlIiwic2V0dGluZyIsInZhbHVlIiwibmV3U2V0dGluZ3MiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVQYXJ0aWNpcGFudFBlcm1pc3Npb25DaGFuZ2UiLCJ1c2VySWQiLCJwZXJtaXNzaW9uIiwibG9nIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJoNCIsImxhYmVsIiwicCIsImlucHV0IiwidHlwZSIsImNoZWNrZWQiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJmaWx0ZXIiLCJyb2xlIiwibWFwIiwicGFydGljaXBhbnQiLCJoNSIsInVzZXIiLCJ1c2VybmFtZSIsInNwYW4iLCJkZWZhdWx0Q2hlY2tlZCIsIl9pZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/moderation/PermissionControl.tsx\n");

/***/ }),

/***/ "(ssr)/./components/studio/StreamStudio.tsx":
/*!********************************************!*\
  !*** ./components/studio/StreamStudio.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamStudio: () => (/* binding */ StreamStudio)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../convex/_generated/api */ \"(ssr)/./convex/_generated/api.js\");\n/* harmony import */ var _moderation_ModerationPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../moderation/ModerationPanel */ \"(ssr)/./components/moderation/ModerationPanel.tsx\");\n/* harmony import */ var _moderation_BreakoutRoomManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../moderation/BreakoutRoomManager */ \"(ssr)/./components/moderation/BreakoutRoomManager.tsx\");\n/* harmony import */ var _moderation_PermissionControl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../moderation/PermissionControl */ \"(ssr)/./components/moderation/PermissionControl.tsx\");\n/* harmony import */ var _video_player__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../video-player */ \"(ssr)/./components/video-player.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamStudio auto */ \n\n\n\n\n\n\n\nfunction StreamStudio({ streamId, currentUserId }) {\n    const stream = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.streams.get, {\n        streamId\n    });\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.users.getCurrentUser);\n    const startStream = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.streams.startStream);\n    const endStream = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.streams.endStream);\n    const createToken = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.livekit.createToken);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('studio');\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Generate LiveKit token when component mounts\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"StreamStudio.useEffect\": ()=>{\n            const generateToken = {\n                \"StreamStudio.useEffect.generateToken\": async ()=>{\n                    try {\n                        const liveKitToken = await createToken({\n                            streamId,\n                            viewerName: currentUser?.username || 'Anonymous'\n                        });\n                        setToken(liveKitToken);\n                    } catch (error) {\n                        console.error('Failed to generate token:', error);\n                    }\n                }\n            }[\"StreamStudio.useEffect.generateToken\"];\n            if (currentUser) {\n                generateToken();\n            }\n        }\n    }[\"StreamStudio.useEffect\"], [\n        createToken,\n        streamId,\n        currentUser\n    ]);\n    const handleStartStream = async ()=>{\n        try {\n            await startStream({\n                streamId\n            });\n        } catch (error) {\n            console.error('Failed to start stream:', error);\n        }\n    };\n    const handleEndStream = async ()=>{\n        try {\n            await endStream({\n                streamId\n            });\n        } catch (error) {\n            console.error('Failed to end stream:', error);\n        }\n    };\n    if (!stream || !currentUser) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n            lineNumber: 63,\n            columnNumber: 12\n        }, this);\n    }\n    const canModerate = currentUser.globalRole === 'master' || currentUser.globalRole === 'admin' || stream.hostId === currentUserId;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex flex-col bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow-sm border-b px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold\",\n                                    children: stream.title\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        \"Status: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `font-medium ${stream.isLive ? 'text-green-600' : 'text-gray-600'}`,\n                                            children: stream.isLive ? 'Live' : 'Offline'\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 23\n                                        }, this),\n                                        stream.participantCount !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-4\",\n                                            children: [\n                                                \"Participants: \",\n                                                stream.participantCount\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                canModerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: !stream.isLive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartStream,\n                                        className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\",\n                                        children: \"Start Stream\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleEndStream,\n                                        className: \"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600\",\n                                        children: \"End Stream\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                canModerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('studio'),\n                                            className: `px-4 py-2 rounded ${activeTab === 'studio' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                                            children: \"Studio\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('moderation'),\n                                            className: `px-4 py-2 rounded ${activeTab === 'moderation' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                                            children: \"Moderation\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('breakout'),\n                                            className: `px-4 py-2 rounded ${activeTab === 'breakout' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                                            children: \"Breakout Rooms\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab('permissions'),\n                                            className: `px-4 py-2 rounded ${activeTab === 'permissions' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,\n                                            children: \"Permissions\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-6\",\n                        children: [\n                            activeTab === 'studio' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow h-full flex items-center justify-center\",\n                                children: token ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_video_player__WEBPACK_IMPORTED_MODULE_7__.VideoPlayer, {\n                                    token: token,\n                                    room: streamId\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: \"Loading video...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'moderation' && canModerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_moderation_ModerationPanel__WEBPACK_IMPORTED_MODULE_4__.ModerationPanel, {\n                                streamId: streamId,\n                                currentUserId: currentUserId\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'breakout' && canModerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_moderation_BreakoutRoomManager__WEBPACK_IMPORTED_MODULE_5__.BreakoutRoomManager, {\n                                streamId: streamId,\n                                currentUserId: currentUserId\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === 'permissions' && canModerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_moderation_PermissionControl__WEBPACK_IMPORTED_MODULE_6__.PermissionControl, {\n                                streamId: streamId,\n                                currentUserId: currentUserId\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-l p-4 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Stream Info\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Host: \",\n                                                    stream.host?.username || 'Unknown'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Max Participants: \",\n                                                    stream.maxParticipants || 50\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    \"Chat: \",\n                                                    stream.isChatEnabled ? 'Enabled' : 'Disabled'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-lg p-4 flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Chat functionality would be implemented here\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/studio/StreamStudio.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/theme-provider.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRStCO0FBQ21DO0FBQzNELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQVk7SUFDdkQscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDIiwic291cmNlcyI6WyIvaG9tZS9uaWNrL3N0cmVhbXlhcmQtY2xvbmV6L2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCI7XG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBhbnkpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+O1xufSJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\nconst Toaster = ({ ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n        className: \"toaster group\",\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/ui/sonner.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3Nvbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFMkM7QUFJM0MsTUFBTUEsVUFBVSxDQUFDLEVBQUUsR0FBR0UsT0FBcUI7SUFDekMscUJBQ0UsOERBQUNELDJDQUFNQTtRQUNMRSxXQUFVO1FBQ1ZDLGNBQWM7WUFDWkMsWUFBWTtnQkFDVkMsT0FDRTtnQkFDRkMsYUFBYTtnQkFDYkMsY0FDRTtnQkFDRkMsY0FDRTtZQUNKO1FBQ0Y7UUFDQyxHQUFHUCxLQUFLOzs7Ozs7QUFHZjtBQUVtQiIsInNvdXJjZXMiOlsiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9jb21wb25lbnRzL3VpL3Nvbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IFRvYXN0ZXIgYXMgU29ubmVyIH0gZnJvbSBcInNvbm5lclwiO1xuXG50eXBlIFRvYXN0ZXJQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzPHR5cGVvZiBTb25uZXI+O1xuXG5jb25zdCBUb2FzdGVyID0gKHsgLi4ucHJvcHMgfTogVG9hc3RlclByb3BzKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPFNvbm5lclxuICAgICAgY2xhc3NOYW1lPVwidG9hc3RlciBncm91cFwiXG4gICAgICB0b2FzdE9wdGlvbnM9e3tcbiAgICAgICAgY2xhc3NOYW1lczoge1xuICAgICAgICAgIHRvYXN0OlxuICAgICAgICAgICAgXCJncm91cCB0b2FzdCBncm91cC1bLnRvYXN0ZXJdOmJnLWJhY2tncm91bmQgZ3JvdXAtWy50b2FzdGVyXTp0ZXh0LWZvcmVncm91bmQgZ3JvdXAtWy50b2FzdGVyXTpib3JkZXItYm9yZGVyIGdyb3VwLVsudG9hc3Rlcl06c2hhZG93LWxnXCIsXG4gICAgICAgICAgZGVzY3JpcHRpb246IFwiZ3JvdXAtWy50b2FzdF06dGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgICAgYWN0aW9uQnV0dG9uOlxuICAgICAgICAgICAgXCJncm91cC1bLnRvYXN0XTpiZy1wcmltYXJ5IGdyb3VwLVsudG9hc3RdOnRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kXCIsXG4gICAgICAgICAgY2FuY2VsQnV0dG9uOlxuICAgICAgICAgICAgXCJncm91cC1bLnRvYXN0XTpiZy1tdXRlZCBncm91cC1bLnRvYXN0XTp0ZXh0LW11dGVkLWZvcmVncm91bmRcIixcbiAgICAgICAgfSxcbiAgICAgIH19XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKTtcbn07XG5cbmV4cG9ydCB7IFRvYXN0ZXIgfTsiXSwibmFtZXMiOlsiVG9hc3RlciIsIlNvbm5lciIsInByb3BzIiwiY2xhc3NOYW1lIiwidG9hc3RPcHRpb25zIiwiY2xhc3NOYW1lcyIsInRvYXN0IiwiZGVzY3JpcHRpb24iLCJhY3Rpb25CdXR0b24iLCJjYW5jZWxCdXR0b24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./components/video-player.tsx":
/*!*************************************!*\
  !*** ./components/video-player.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VideoPlayer: () => (/* binding */ VideoPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/hooks-COF-7zxu.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/components-k0KtCs0w.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/prefabs.mjs\");\n/* harmony import */ var _livekit_components_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @livekit/components-react */ \"(ssr)/./node_modules/@livekit/components-react/dist/room-DhBnHppi.mjs\");\n/* harmony import */ var livekit_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! livekit-client */ \"(ssr)/./node_modules/livekit-client/dist/livekit-client.esm.mjs\");\n/* harmony import */ var _livekit_components_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @livekit/components-styles */ \"(ssr)/./node_modules/@livekit/components-styles/dist/general/index.css\");\n/* __next_internal_client_entry_do_not_use__ VideoPlayer auto */ \n\n\n\n\nfunction CustomVideoConference() {\n    const tracks = (0,_livekit_components_react__WEBPACK_IMPORTED_MODULE_4__.t)([\n        {\n            source: livekit_client__WEBPACK_IMPORTED_MODULE_2__.Track.Source.Camera,\n            withPlaceholder: true\n        },\n        {\n            source: livekit_client__WEBPACK_IMPORTED_MODULE_2__.Track.Source.ScreenShare,\n            withPlaceholder: false\n        }\n    ], {\n        onlySubscribed: false\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_5__.G, {\n                tracks: tracks,\n                style: {\n                    height: 'calc(100% - 80px)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_5__.P, {}, void 0, false, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/video-player.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/video-player.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_6__.ControlBar, {}, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/video-player.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/video-player.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\nfunction VideoPlayer({ token, room }) {\n    const serverUrl = \"wss://streamyard-clonez-1zofz2li.livekit.cloud\" || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full bg-black rounded-lg overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_7__.L, {\n            video: true,\n            audio: true,\n            token: token,\n            serverUrl: serverUrl,\n            \"data-lk-theme\": \"default\",\n            style: {\n                height: '100%'\n            },\n            onConnected: ()=>console.log('Connected to room:', room),\n            onDisconnected: ()=>console.log('Disconnected from room:', room),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomVideoConference, {}, void 0, false, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/video-player.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_livekit_components_react__WEBPACK_IMPORTED_MODULE_5__.R, {}, void 0, false, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/video-player.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/streamyard-clonez/components/video-player.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/video-player.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/video-player.tsx\n");

/***/ }),

/***/ "(ssr)/./convex/_generated/api.js":
/*!**********************************!*\
  !*** ./convex/_generated/api.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   internal: () => (/* binding */ internal)\n/* harmony export */ });\n/* harmony import */ var convex_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! convex/server */ \"(ssr)/./node_modules/convex/dist/esm/server/index.js\");\n/* eslint-disable */ /**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */ \n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */ const api = convex_server__WEBPACK_IMPORTED_MODULE_0__.anyApi;\nconst internal = convex_server__WEBPACK_IMPORTED_MODULE_0__.anyApi;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb252ZXgvX2dlbmVyYXRlZC9hcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsa0JBQWtCLEdBQ2xCOzs7Ozs7O0NBT0MsR0FFc0M7QUFFdkM7Ozs7Ozs7Q0FPQyxHQUNNLE1BQU1DLE1BQU1ELGlEQUFNQSxDQUFDO0FBQ25CLE1BQU1FLFdBQVdGLGlEQUFNQSxDQUFDIiwic291cmNlcyI6WyIvaG9tZS9uaWNrL3N0cmVhbXlhcmQtY2xvbmV6L2NvbnZleC9fZ2VuZXJhdGVkL2FwaS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiBlc2xpbnQtZGlzYWJsZSAqL1xuLyoqXG4gKiBHZW5lcmF0ZWQgYGFwaWAgdXRpbGl0eS5cbiAqXG4gKiBUSElTIENPREUgSVMgQVVUT01BVElDQUxMWSBHRU5FUkFURUQuXG4gKlxuICogVG8gcmVnZW5lcmF0ZSwgcnVuIGBucHggY29udmV4IGRldmAuXG4gKiBAbW9kdWxlXG4gKi9cblxuaW1wb3J0IHsgYW55QXBpIH0gZnJvbSBcImNvbnZleC9zZXJ2ZXJcIjtcblxuLyoqXG4gKiBBIHV0aWxpdHkgZm9yIHJlZmVyZW5jaW5nIENvbnZleCBmdW5jdGlvbnMgaW4geW91ciBhcHAncyBBUEkuXG4gKlxuICogVXNhZ2U6XG4gKiBgYGBqc1xuICogY29uc3QgbXlGdW5jdGlvblJlZmVyZW5jZSA9IGFwaS5teU1vZHVsZS5teUZ1bmN0aW9uO1xuICogYGBgXG4gKi9cbmV4cG9ydCBjb25zdCBhcGkgPSBhbnlBcGk7XG5leHBvcnQgY29uc3QgaW50ZXJuYWwgPSBhbnlBcGk7XG4iXSwibmFtZXMiOlsiYW55QXBpIiwiYXBpIiwiaW50ZXJuYWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./convex/_generated/api.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(dashboard)/stream/[streamId]/page.tsx */ \"(ssr)/./app/(dashboard)/stream/[streamId]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRmhvbWUlMkZuaWNrJTJGc3RyZWFteWFyZC1jbG9uZXolMkZhcHAlMkYoZGFzaGJvYXJkKSUyRnN0cmVhbSUyRiU1QnN0cmVhbUlkJTVEJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUE0RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvbmljay9zdHJlYW15YXJkLWNsb25lei9hcHAvKGRhc2hib2FyZCkvc3RyZWFtL1tzdHJlYW1JZF0vcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/convex-provider.tsx */ \"(ssr)/./components/convex-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/header.tsx */ \"(ssr)/./components/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/livepeer-provider.tsx */ \"(ssr)/./components/livepeer-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(ssr)/./components/ui/sonner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fconvex-provider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Flivepeer-provider.tsx%22%2C%22ids%22%3A%5B%22LivepeerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fcomponents%2Fui%2Fsonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fhome%2Fnick%2Fstreamyard-clonez%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/next","vendor-chunks/convex","vendor-chunks/swr","vendor-chunks/@swc","vendor-chunks/@livekit","vendor-chunks/dequal","vendor-chunks/snakecase-keys","vendor-chunks/use-sync-external-store","vendor-chunks/tslib","vendor-chunks/sonner","vendor-chunks/next-themes","vendor-chunks/livekit-client","vendor-chunks/jwt-decode","vendor-chunks/snake-case","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/dot-case","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&page=%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&appPaths=%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fstream%2F%5BstreamId%5D%2Fpage.tsx&appDir=%2Fhome%2Fnick%2Fstreamyard-clonez%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fnick%2Fstreamyard-clonez&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();