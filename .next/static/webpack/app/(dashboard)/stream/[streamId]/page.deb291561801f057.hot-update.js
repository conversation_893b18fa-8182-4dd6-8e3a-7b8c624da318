"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/stream/[streamId]/page",{

/***/ "(app-pages-browser)/./components/studio/StreamStudio.tsx":
/*!********************************************!*\
  !*** ./components/studio/StreamStudio.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamStudio: () => (/* binding */ StreamStudio)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var _convex_generated_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../convex/_generated/api */ \"(app-pages-browser)/./convex/_generated/api.js\");\n/* harmony import */ var _moderation_ModerationPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../moderation/ModerationPanel */ \"(app-pages-browser)/./components/moderation/ModerationPanel.tsx\");\n/* harmony import */ var _video_player__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../video-player */ \"(app-pages-browser)/./components/video-player.tsx\");\n/* harmony import */ var _LayoutManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LayoutManager */ \"(app-pages-browser)/./components/studio/LayoutManager.tsx\");\n/* harmony import */ var _CustomLayoutBuilder__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./CustomLayoutBuilder */ \"(app-pages-browser)/./components/studio/CustomLayoutBuilder.tsx\");\n/* harmony import */ var _BrandingPanel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./BrandingPanel */ \"(app-pages-browser)/./components/studio/BrandingPanel.tsx\");\n/* harmony import */ var _GuestManager__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./GuestManager */ \"(app-pages-browser)/./components/studio/GuestManager.tsx\");\n/* harmony import */ var _StreamDestinations__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./StreamDestinations */ \"(app-pages-browser)/./components/studio/StreamDestinations.tsx\");\n/* harmony import */ var _StreamMonitor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./StreamMonitor */ \"(app-pages-browser)/./components/studio/StreamMonitor.tsx\");\n/* harmony import */ var _StreamChat__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./StreamChat */ \"(app-pages-browser)/./components/studio/StreamChat.tsx\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/screen-share.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,Image,Layers,MessageCircle,Mic,MicOff,Minimize2,Monitor,MoreHorizontal,Play,ScreenShare,Settings,Square,Type,UserPlus,Users,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* __next_internal_client_entry_do_not_use__ StreamStudio auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction StreamStudio(param) {\n    let { streamId, currentUserId } = param;\n    _s();\n    const stream = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.streams.get, {\n        streamId\n    });\n    const currentUser = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.users.getCurrentUser);\n    const startStream = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.streams.startStream);\n    const endStream = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.streams.endStream);\n    const createToken = (0,convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction)(_convex_generated_api__WEBPACK_IMPORTED_MODULE_3__.api.livekit.createToken);\n    const [activePanel, setActivePanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('none');\n    const [selectedLayout, setSelectedLayout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('single');\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMuted, setIsMuted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isVideoOff, setIsVideoOff] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScreenSharing, setIsScreenSharing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLayoutBuilder, setShowLayoutBuilder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBrandingPanel, setShowBrandingPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showGuestManager, setShowGuestManager] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDestinations, setShowDestinations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMonitor, setShowMonitor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [customLayouts, setCustomLayouts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [brandElements, setBrandElements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [guests, setGuests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [destinations, setDestinations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isChatVisible, setIsChatVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // StreamYard layout presets\n    const layouts = [\n        {\n            id: 'single',\n            name: 'Single',\n            icon: '👤'\n        },\n        {\n            id: 'group',\n            name: 'Group',\n            icon: '👥'\n        },\n        {\n            id: 'spotlight',\n            name: 'Spotlight',\n            icon: '⭐'\n        },\n        {\n            id: 'news',\n            name: 'News',\n            icon: '📺'\n        },\n        {\n            id: 'screen',\n            name: 'Screen',\n            icon: '🖥️'\n        },\n        {\n            id: 'pip',\n            name: 'Picture-in-Picture',\n            icon: '📱'\n        },\n        {\n            id: 'cinema',\n            name: 'Cinema',\n            icon: '🎬'\n        }\n    ];\n    // Generate LiveKit token when component mounts\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"StreamStudio.useEffect\": ()=>{\n            const generateToken = {\n                \"StreamStudio.useEffect.generateToken\": async ()=>{\n                    try {\n                        const liveKitToken = await createToken({\n                            streamId,\n                            viewerName: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.username) || 'Anonymous'\n                        });\n                        setToken(liveKitToken);\n                    } catch (error) {\n                        console.error('Failed to generate token:', error);\n                    }\n                }\n            }[\"StreamStudio.useEffect.generateToken\"];\n            if (currentUser) {\n                generateToken();\n            }\n        }\n    }[\"StreamStudio.useEffect\"], [\n        createToken,\n        streamId,\n        currentUser\n    ]);\n    const handleStartStream = async ()=>{\n        try {\n            await startStream({\n                streamId\n            });\n        } catch (error) {\n            console.error('Failed to start stream:', error);\n        }\n    };\n    const handleEndStream = async ()=>{\n        try {\n            await endStream({\n                streamId\n            });\n        } catch (error) {\n            console.error('Failed to end stream:', error);\n        }\n    };\n    const toggleMute = ()=>setIsMuted(!isMuted);\n    const toggleVideo = ()=>setIsVideoOff(!isVideoOff);\n    const toggleScreenShare = ()=>setIsScreenSharing(!isScreenSharing);\n    const toggleRecording = ()=>setIsRecording(!isRecording);\n    const handleLayoutChange = (layoutId)=>{\n        setSelectedLayout(layoutId);\n        // Here you would implement the actual layout change logic\n        console.log('Layout changed to:', layoutId);\n    };\n    const handleCreateCustomLayout = ()=>{\n        setShowLayoutBuilder(true);\n    };\n    const handleSaveCustomLayout = (layout)=>{\n        setCustomLayouts([\n            ...customLayouts,\n            layout\n        ]);\n        setSelectedLayout(layout.id);\n    };\n    if (!stream || !currentUser) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white text-lg\",\n                children: \"Loading studio...\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, this);\n    }\n    const canModerate = currentUser.globalRole === 'master' || currentUser.globalRole === 'admin' || stream.hostId === currentUserId;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex flex-col bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border-b border-gray-700 px-4 md:px-6 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 md:space-x-4 min-w-0 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg md:text-xl font-semibold text-white truncate\",\n                                    children: stream.title\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-3 rounded-full \".concat(stream.isLive ? 'bg-red-500 animate-pulse' : 'bg-gray-500')\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: stream.isLive ? 'LIVE' : 'OFFLINE'\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        stream.participantCount !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 hidden sm:flex items-center ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                stream.participantCount\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 md:space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 md:space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowMonitor(true),\n                                            className: \"flex items-center space-x-1 md:space-x-2 text-sm text-gray-300 hover:text-white transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"0 viewers\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowDestinations(true),\n                                            className: \"flex items-center space-x-1 md:space-x-2 text-sm text-gray-300 hover:text-white transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden md:inline\",\n                                                    children: \"Destinations\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this),\n                                canModerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: !stream.isLive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartStream,\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Go Live\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleEndStream,\n                                        className: \"flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"End Stream\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActivePanel(activePanel === 'settings' ? 'none' : 'settings'),\n                                    className: \"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full bg-black rounded-lg relative overflow-hidden border border-gray-700\",\n                                    children: [\n                                        token ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_video_player__WEBPACK_IMPORTED_MODULE_5__.VideoPlayer, {\n                                            token: token,\n                                            room: streamId\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center h-full text-gray-400\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-16 h-16 mx-auto mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Loading studio...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4 flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowBrandingPanel(true),\n                                                    className: \"p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors\",\n                                                    title: \"Branding & Graphics\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowBrandingPanel(true),\n                                                    className: \"p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors\",\n                                                    title: \"Add Logo\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowBrandingPanel(true),\n                                                    className: \"p-2 bg-black/50 text-white rounded-lg hover:bg-black/70 transition-colors\",\n                                                    title: \"Add Text\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 right-4 flex items-center space-x-2 bg-red-600 text-white px-3 py-1 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-white rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"REC\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 border-t border-gray-700 p-2 md:p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row md:items-center md:justify-between space-y-3 md:space-y-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 overflow-x-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LayoutManager__WEBPACK_IMPORTED_MODULE_6__.LayoutManager, {\n                                                selectedLayout: selectedLayout,\n                                                onLayoutChange: handleLayoutChange,\n                                                onCreateCustomLayout: handleCreateCustomLayout,\n                                                customLayouts: customLayouts\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center md:justify-start space-x-2 md:ml-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowGuestManager(true),\n                                                className: \"flex items-center space-x-2 px-3 py-2 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"hidden sm:inline\",\n                                                        children: \"Invite Guests\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sm:hidden\",\n                                                        children: \"Invite\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    activePanel !== 'none' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full md:w-80 bg-gray-800 border-l border-gray-700 flex flex-col absolute md:relative inset-0 md:inset-auto z-10 md:z-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: [\n                                                activePanel === 'chat' && 'Chat',\n                                                activePanel === 'participants' && 'Participants',\n                                                activePanel === 'settings' && 'Settings'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActivePanel('none'),\n                                            className: \"p-1 text-gray-400 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 p-4 overflow-y-auto\",\n                                children: [\n                                    activePanel === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full -m-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StreamChat__WEBPACK_IMPORTED_MODULE_12__.StreamChat, {\n                                            streamId: streamId,\n                                            currentUserId: currentUserId,\n                                            isModerator: canModerate,\n                                            isVisible: isChatVisible,\n                                            onToggleVisibility: ()=>setIsChatVisible(!isChatVisible)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this),\n                                    activePanel === 'participants' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Invite Guests\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: \"No participants yet\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this),\n                                    activePanel === 'settings' && canModerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_moderation_ModerationPanel__WEBPACK_IMPORTED_MODULE_4__.ModerationPanel, {\n                                            streamId: streamId,\n                                            currentUserId: currentUserId\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 border-t border-gray-700 p-2 md:p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 md:space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMute,\n                                    className: \"p-2 md:p-3 rounded-lg transition-colors \".concat(isMuted ? 'bg-red-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'),\n                                    children: isMuted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"w-4 h-4 md:w-5 md:h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 26\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"w-4 h-4 md:w-5 md:h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 73\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleVideo,\n                                    className: \"p-2 md:p-3 rounded-lg transition-colors \".concat(isVideoOff ? 'bg-red-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'),\n                                    children: isVideoOff ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"w-4 h-4 md:w-5 md:h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 29\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        className: \"w-4 h-4 md:w-5 md:h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 78\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleScreenShare,\n                                    className: \"p-2 md:p-3 rounded-lg transition-colors \".concat(isScreenSharing ? 'bg-blue-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        className: \"w-4 h-4 md:w-5 md:h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 md:space-x-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleRecording,\n                                className: \"flex items-center space-x-1 md:space-x-2 px-2 md:px-4 py-2 rounded-lg transition-colors text-sm \".concat(isRecording ? 'bg-red-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Record, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden sm:inline\",\n                                        children: isRecording ? 'Stop Recording' : 'Record'\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sm:hidden\",\n                                        children: isRecording ? 'Stop' : 'Rec'\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 md:space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActivePanel(activePanel === 'chat' ? 'none' : 'chat'),\n                                    className: \"p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        className: \"w-4 h-4 md:w-5 md:h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActivePanel(activePanel === 'participants' ? 'none' : 'participants'),\n                                    className: \"p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4 md:w-5 md:h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"p-2 md:p-3 bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_Image_Layers_MessageCircle_Mic_MicOff_Minimize2_Monitor_MoreHorizontal_Play_ScreenShare_Settings_Square_Type_UserPlus_Users_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        className: \"w-4 h-4 md:w-5 md:h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CustomLayoutBuilder__WEBPACK_IMPORTED_MODULE_7__.CustomLayoutBuilder, {\n                isOpen: showLayoutBuilder,\n                onClose: ()=>setShowLayoutBuilder(false),\n                onSave: handleSaveCustomLayout\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 443,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BrandingPanel__WEBPACK_IMPORTED_MODULE_8__.BrandingPanel, {\n                isOpen: showBrandingPanel,\n                onClose: ()=>setShowBrandingPanel(false),\n                elements: brandElements,\n                onUpdateElements: setBrandElements\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GuestManager__WEBPACK_IMPORTED_MODULE_9__.GuestManager, {\n                isOpen: showGuestManager,\n                onClose: ()=>setShowGuestManager(false),\n                guests: guests,\n                onUpdateGuests: setGuests,\n                streamId: streamId\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StreamDestinations__WEBPACK_IMPORTED_MODULE_10__.StreamDestinations, {\n                isOpen: showDestinations,\n                onClose: ()=>setShowDestinations(false),\n                destinations: destinations,\n                onUpdateDestinations: setDestinations\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 467,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StreamMonitor__WEBPACK_IMPORTED_MODULE_11__.StreamMonitor, {\n                isOpen: showMonitor,\n                onClose: ()=>setShowMonitor(false),\n                streamId: streamId,\n                isLive: stream.isLive || false\n            }, void 0, false, {\n                fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n                lineNumber: 475,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/streamyard-clonez/components/studio/StreamStudio.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(StreamStudio, \"4FFLJidPIurr3B2Hz9hv7SuJZig=\", false, function() {\n    return [\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useQuery,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useMutation,\n        convex_react__WEBPACK_IMPORTED_MODULE_2__.useAction\n    ];\n});\n_c = StreamStudio;\nvar _c;\n$RefreshReg$(_c, \"StreamStudio\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/studio/StreamStudio.tsx\n"));

/***/ })

});