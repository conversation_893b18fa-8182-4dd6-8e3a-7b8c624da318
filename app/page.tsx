"use client";

import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { useUser } from "@clerk/nextjs";
import Link from "next/link";
import { Play, Users, Video, TrendingUp, Clock, Plus } from "lucide-react";

export default function Home() {
  const { user } = useUser();
  const myStreams = useQuery(api.streams.getMyStreams);
  const analytics = useQuery(api.streams.getStreamAnalytics);

  const liveStreams = myStreams?.filter((stream: any) => stream.isLive) || [];
  const recentStreams = myStreams?.slice(0, 3) || [];

  if (!user) {
    return (
      <main className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
        <div className="flex min-h-screen flex-col items-center justify-center p-8">
          <div className="text-center space-y-6 max-w-4xl">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-pink-400 to-purple-600 bg-clip-text text-transparent">
              StreamYard <PERSON>
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
              Create professional live streams with interactive features, moderation tools, and real-time chat.
            </p>
            <div className="flex gap-4 justify-center mt-8">
              <Link
                href="/sign-in"
                className="px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all duration-200 transform hover:scale-105"
              >
                Get Started
              </Link>
              <Link
                href="/sign-up"
                className="px-8 py-3 border border-purple-400 text-purple-400 rounded-lg font-semibold hover:bg-purple-400 hover:text-white transition-all duration-200"
              >
                Sign Up
              </Link>
            </div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8 animate-fade-in">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2">
            Welcome back, {user.firstName}
          </h1>
          <p className="text-gray-400 text-lg">Manage your streams and grow your audience</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 animate-slide-up">
          <div className="bg-gradient-to-br from-blue-500/20 to-blue-600/20 backdrop-blur-sm border border-blue-500/30 rounded-xl p-6 hover:from-blue-500/30 hover:to-blue-600/30 hover:scale-105 transition-all duration-300 group">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-400 text-sm font-medium">Live Streams</p>
                <p className="text-3xl font-bold text-white">{analytics?.liveStreams ?? 0}</p>
              </div>
              <div className="p-3 bg-blue-500/20 rounded-lg group-hover:animate-float">
                <Video className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-500/20 to-green-600/20 backdrop-blur-sm border border-green-500/30 rounded-xl p-6 hover:from-green-500/30 hover:to-green-600/30 hover:scale-105 transition-all duration-300 group">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-400 text-sm font-medium">Total Streams</p>
                <p className="text-3xl font-bold text-white">{analytics?.totalStreams ?? 0}</p>
              </div>
              <div className="p-3 bg-green-500/20 rounded-lg group-hover:animate-float">
                <Play className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-500/20 to-purple-600/20 backdrop-blur-sm border border-purple-500/30 rounded-xl p-6 hover:from-purple-500/30 hover:to-purple-600/30 hover:scale-105 transition-all duration-300 group">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-400 text-sm font-medium">Active Viewers</p>
                <p className="text-3xl font-bold text-white">{analytics?.totalViewers ?? 0}</p>
              </div>
              <div className="p-3 bg-purple-500/20 rounded-lg group-hover:animate-float">
                <Users className="w-6 h-6 text-purple-400" />
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-500/20 to-orange-600/20 backdrop-blur-sm border border-orange-500/30 rounded-xl p-6 hover:from-orange-500/30 hover:to-orange-600/30 hover:scale-105 transition-all duration-300 group">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-400 text-sm font-medium">Growth</p>
                <p className="text-3xl font-bold text-white">{analytics?.growth ?? '0%'}</p>
              </div>
              <div className="p-3 bg-orange-500/20 rounded-lg group-hover:animate-float">
                <TrendingUp className="w-6 h-6 text-orange-400" />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-4">Quick Actions</h2>
          <div className="flex flex-wrap gap-4">
            <Link
              href="/create"
              className="flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all duration-200 transform hover:scale-105"
            >
              <Plus className="w-5 h-5" />
              Create New Stream
            </Link>
          </div>
        </div>

        {/* Recent Streams */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 animate-scale-in">
          {/* Live Streams */}
          <div className="bg-black/40 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 hover:border-gray-600/50 transition-all duration-300">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              Live Streams
            </h3>
            {liveStreams.length > 0 ? (
              <div className="space-y-3">
                {liveStreams.map((stream: any) => (
                  <div key={stream._id} className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg border border-red-500/30">
                    <div>
                      <h4 className="font-semibold text-white">{stream.title}</h4>
                      <p className="text-sm text-gray-400">{stream.description}</p>
                    </div>
                    <Link
                      href={`/stream/${stream._id}`}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                      Join
                    </Link>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-400 text-center py-8">No live streams</p>
            )}
          </div>

          {/* Recent Streams */}
          <div className="bg-black/40 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 hover:border-gray-600/50 transition-all duration-300">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Recent Streams
            </h3>
            {recentStreams.length > 0 ? (
              <div className="space-y-3">
                {recentStreams.map((stream: any) => (
                  <div key={stream._id} className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg">
                    <div>
                      <h4 className="font-semibold text-white">{stream.title}</h4>
                      <p className="text-sm text-gray-400">{stream.description}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          stream.isLive 
                            ? 'bg-red-500/20 text-red-400' 
                            : 'bg-gray-500/20 text-gray-400'
                        }`}>
                          {stream.isLive ? 'Live' : 'Offline'}
                        </span>
                      </div>
                    </div>
                    <Link
                      href={`/stream/${stream._id}`}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      View
                    </Link>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-400 mb-4">No streams yet</p>
                <Link
                  href="/create"
                  className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  Create Your First Stream
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}
