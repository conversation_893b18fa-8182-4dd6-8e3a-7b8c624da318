{"name": "streamyard-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "npm-run-all --parallel dev:backend dev:frontend", "build": "tsc && next build 2>&1", "dev:backend": "convex dev", "dev:frontend": "next dev", "predev": "convex dev --until-success", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.24.0", "@livekit/components-react": "^2.0.0", "@livekit/components-styles": "^1.0.0", "@livepeer/react": "^4.3.6", "clsx": "^2.1.1", "convex": "^1.25.2", "livekit-client": "^2.0.0", "livekit-server-sdk": "^2.0.0", "lucide-react": "^0.525.0", "next": "^15.3.5", "next-themes": "^0.4.6", "npm-run-all2": "^8.0.4", "react": "^18.0.0", "react-dom": "^18.0.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.0", "zod": "^3.22.0"}, "devDependencies": {"@types/node": "20.19.6", "@types/react": "18.3.23", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-next": "14.0.0", "typescript": "5.8.3"}}