"use node";

import { v } from "convex/values";
import { action } from "./_generated/server";
import { api } from "./_generated/api";
import { AccessToken } from "livekit-server-sdk";

export const createToken = action({
  args: {
    streamId: v.id("streams"),
    viewerName: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();

    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get stream info
    const stream = await ctx.runQuery(api.streams.get, { streamId: args.streamId });
    if (!stream) {
      throw new Error("Stream not found");
    }

    // Check if user is banned
    const user = await ctx.runQuery(api.users.getUser, { userId: identity.subject });
    if (user?.isBanned) {
      throw new Error("User is banned");
    }

    // Get or create participant record
    let participant = await ctx.runQuery(api.participants.getParticipant, { 
      streamId: args.streamId, 
      userId: identity.subject 
    });

    if (!participant) {
      // Auto-join as viewer if not already a participant
      await ctx.runMutation(api.participants.joinStream, {
        streamId: args.streamId,
        requestedRole: stream.hostId === identity.subject ? "host" : "viewer",
      });
      
      participant = await ctx.runQuery(api.participants.getParticipant, { 
        streamId: args.streamId, 
        userId: identity.subject 
      });
    }

    if (!participant) {
      throw new Error("Failed to create participant");
    }

    // Check for active moderations
    const moderationStatus = await ctx.runQuery(api.moderation.getActiveModerationsForUser, {
      streamId: args.streamId,
      userId: identity.subject,
    });

    if (moderationStatus?.isBanned) {
      throw new Error("User is banned from this stream");
    }

    if (moderationStatus?.isTimedOut) {
      throw new Error("User is timed out from this stream");
    }

    // Determine permissions based on role
    let canPublish = false;
    let canSubscribe = true;

    switch (participant.role) {
      case "host":
      case "co-host":
        canPublish = true;
        break;
      case "moderator":
      case "guest":
        canPublish = !moderationStatus?.isMuted;
        break;
      case "viewer":
        canPublish = false;
        break;
    }

    const at = new AccessToken(process.env.LIVEKIT_API_KEY, process.env.LIVEKIT_API_SECRET, {
      identity: identity.subject,
      name: args.viewerName,
    });

    at.addGrant({
      roomJoin: true,
      room: args.streamId,
      canPublish,
      canSubscribe,
    });

    return await at.toJwt();
  },
});