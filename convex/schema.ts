import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  streams: defineTable({
    title: v.string(),
    description: v.string(),
    hostId: v.string(),
    isLive: v.boolean(),
    isChatEnabled: v.boolean(),
    isChatDelayed: v.boolean(),
    isChatFollowersOnly: v.boolean(),
    maxParticipants: v.optional(v.number()),
    streamKey: v.optional(v.string()),
  }).index("by_host", ["hostId"]),
  
  users: defineTable({
    userId: v.string(),
    globalRole: v.union(v.literal("master"), v.literal("admin"), v.literal("user")),
    username: v.string(),
    email: v.string(),
    isBanned: v.boolean(),
  }).index("by_user_id", ["userId"]),
  
  streamParticipants: defineTable({
    streamId: v.id("streams"),
    userId: v.string(),
    role: v.union(
      v.literal("host"),
      v.literal("co-host"),
      v.literal("moderator"),
      v.literal("guest"),
      v.literal("viewer")
    ),
    joinedAt: v.number(),
    isActive: v.boolean(),
  })
    .index("by_stream", ["streamId"])
    .index("by_user_stream", ["userId", "streamId"]),
  
  moderationLogs: defineTable({
    streamId: v.id("streams"),
    moderatorId: v.string(),
    targetUserId: v.string(),
    action: v.union(
      v.literal("mute"),
      v.literal("unmute"),
      v.literal("timeout"),
      v.literal("kick"),
      v.literal("ban")
    ),
    reason: v.optional(v.string()),
    duration: v.optional(v.number()),
    timestamp: v.number(),
  })
    .index("by_stream", ["streamId"])
    .index("by_target", ["targetUserId"]),
});